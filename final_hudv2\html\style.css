@import url("https://fonts.cdnfonts.com/css/bebas-neue");
@import url("https://fonts.cdnfonts.com/css/svn-gilroy?styles=55332,55331");
:root {
  --ff-bebas: "Chalet", sans-serif;
  --color-white: rgba(255, 255, 255, 1);
  --ff-header: "Chalet", sans-serif;
}
*,
::before,
::after {
  box-sizing: border-box;
  margin: 0px;
  padding: 0px;
}
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: var(--ff-header);
  user-select: none;
  color: var(--color-white);
}
input {
  border: none;
  outline: none;
  background-color: transparent;
}

#weaponContainer {
  display: none;
}

.main__corleone-inventory-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120vh;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1vh;
}
.main__corleone-inventory-left,
.main__corleone-inventory-right {
  width: 60.5vh;
  height: 100%;
}
.main__corleone-property-grid-item-header {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.25vh;
  text-shadow: rgba(0, 0, 0, 0.5) 0.2vh 0.2vh 0vh;
}
.main__corleone-inventory-header-p {
  font-size: 1.75vh;
  color: rgb(255, 255, 255);
  font-family: var(--ff-body) !important;
}
.main__corleone-property-grid-item-header-left {
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  font-size: 3vh;
  font-weight: 300;
  font-family: var(--ff-bebas);
}
.main__corleone-property-grid-item-header-left p span {
  font-weight: 500;
  color: rgb(255, 255, 255);
}
.main__corleone-property-grid-item-header-right-btn {
  width: 2.25vh;
  height: 2.25vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to top, rgb(255, 9, 53), rgb(190, 59, 66));
  color: rgb(255, 255, 255);
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease-in 0s;
  font-size: 1.25vh;
}
.main__corleone-property-grid-item-header-right-btn i {
  transition: all 0.2s ease-in 0s;
}
.main__corleone-property-grid-item-header-right-btn:hover {
  box-shadow: rgb(190, 59, 66) 0px 0px 1.7vh;
}
.main__corleone-property-grid-item-header-right-btn:hover i {
  transform: rotate(90deg);
}
.main__corleone-property-grid-item-header-border {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 0.1vh;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.25),
    rgba(255, 255, 255, 0)
  );
}
.main__corleone-property-grid-item-header-border-highlight {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 18.5vh;
  height: 0.1vh;
  background: rgb(140, 3, 253);
  box-shadow: rgb(140, 3, 253) 0px 0px 1.7vh;
}
.main__corleone-property-grid-item-header-border-highlight-2 {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 21vh;
  height: 0.1vh;
  background: rgb(140, 3, 253);
  box-shadow: rgb(140, 3, 253) 0px 0px 1.7vh;
}
.main__corleone-property-grid-item-header-border-highlight-3 {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 9.5vh;
  height: 0.1vh;
  background: rgb(140, 3, 253);
  box-shadow: rgb(140, 3, 253) 0px 0px 1.7vh;
}
.main__corleone-inventory-left-grid-container {
  width: 59.5vh;
  height: 49.25vh;
  border-radius: 0.5vh;
  background: rgba(0, 0, 0, 0.5);
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  padding: 1vh;
  margin-top: 1vh;
}
.main__corleone-inventory-left-grid-scroll-container {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1vh;
  padding-right: 1vh;
  overflow: hidden scroll;
  align-content: flex-start;
  transform: translateZ(0px) translateZ(0px);
  position: relative;
  transition: all 0.2s ease-in 0s;
}
.main__corleone-inventory-left-grid-scroll-container::-webkit-scrollbar {
  width: 0.3vh;
  border-radius: 5vh;
}
.main__corleone-inventory-left-grid-scroll-container::-webkit-scrollbar-track {
  width: 0.3vh;
  border-radius: 5vh;
  background: rgba(255, 255, 255, 0.05);
}
.main__corleone-inventory-left-grid-scroll-container::-webkit-scrollbar-thumb {
  width: 0.3vh;
  border-radius: 5vh;
  background: rgb(140, 3, 253);
}
.main__corleone-inventory-info-header {
  margin-top: 1vh;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1vh;
}
.main__corleone-inventory-info-count {
  position: relative;
  color: rgb(255, 255, 255);
  background: rgba(0, 0, 0, 0.5);
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  height: 3vh;
  width: 10vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2.5vh;
  font-weight: 500;
}
.main__corleone-inventory-info-count p span {
  font-weight: 300;
  color: rgba(255, 255, 255, 0.5);
}
.main__corleone-inventory-info-progress {
  width: 37.5vh;
  height: 3vh;
  background: rgba(0, 0, 0, 0.5);
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  padding: 0.5vh;
}
.main__corleone-inventory-info-progress-2 {
  width: 23vh;
  height: 3vh;
  background: rgba(0, 0, 0, 0.5);
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  padding: 0.5vh;
}
.main__corleone-inventory-info-progress-fill {
  width: 0%;
  height: 100%;
  background: linear-gradient(to right, rgb(174, 0, 255), rgba(184, 28, 37, 0));
  box-shadow: rgb(140, 3, 253) 0px 0px 3.7vh inset,
    rgba(109, 28, 184, 0.55) 0px 0.4vh 3vh;
  border-radius: 0.25vh;
  transition: all 0.2s ease-in-out 0s;
}
.main__corleone-inventory-info-progress-input input {
  width: 10vh;
  background: rgba(0, 0, 0, 0.5);
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  font-size: 2.5vh;
  font-family: var(--ff-body);
  color: rgb(255, 255, 255);
  height: 3vh;
  text-align: center;
  padding: 0.5vh;
}
.main__corleone-inventory-info-progress-input input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}
.main__corleone-inventory-left-grid-item {
  position: relative;
  width: 13.27vh;
  height: 15vh;
  background: radial-gradient(
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.05)
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.25vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5vh;
  cursor: pointer;
  transition: all 0.2s ease-in 0s;
}
.main__corleone-inventory-left-grid-item.notrans {
  transition: unset;
}
.main__corleone-inventory-left-grid-item:hover,
.main__corleone-inventory-left-grid-item.active {
  box-shadow: rgba(255, 255, 255, 0.486) 0px 0px 6vh inset;
  border: 0.1vh solid rgb(255, 255, 255);
}
.main__corleone-inventory-left-grid-item-img {
  width: 8.5vh;
  height: 7.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.main__corleone-inventory-left-grid-item-img img {
  max-width: 100%;
  max-height: 100%;
}
.main__corleone-inventory-left-grid-item-count {
  font-size: 1.25vh;
  color: rgb(255, 255, 255);
  background: radial-gradient(
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.05)
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  padding: 0.25vh 0.5vh;
  border-radius: 0.25vh;
}
.main__corleone-inventory-left-grid-item-name {
  font-size: 1.4vh;
  color: rgb(255, 255, 255);
  text-align: center;
  white-space: normal;
  max-width: 100%;
  word-break: break-all;
}
.main__corleone-inventory-left-grid-item-name.sm-14 {
  font-size: 1.35vh;
}
.main__corleone-inventory-left-grid-item-name.sm-13 {
  font-size: 1.25vh;
}
.main__corleone-inventory-left-grid-item-name.sm-12 {
  font-size: 1.1vh;
}
.main__corleone-inventory-left-grid-item-name.sm-11 {
  font-size: 1vh;
}
.main__corleone-inventory-left-grid-item-name.sm-10 {
  font-size: 0.9vh;
}
.main__corleone-inventory-left-grid-item-name.sm-09 {
  font-size: 0.8vh;
}
.main__corleone-inventory-left-grid-item-name.sm-08 {
  font-size: 0.7vh;
}
.main__corleone-inventory-left-grid-item-popup-container {
  position: absolute;
  padding: 0.5vh;
  background: radial-gradient(rgb(0, 0, 0), rgba(0, 0, 0, 0.75));
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  display: flex;
  flex-direction: column;
  box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 1.7vh;
  gap: 0.5vh;
  z-index: 1000;
}
.main__corleone-inventory-left-grid-item-popup-btn {
  padding: 0.5vh 2vh;
  font-size: 2.5vh;
  color: rgb(255, 255, 255);
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  border-radius: 0.25vh;
  gap: 1vh;
  background: radial-gradient(
    165% 163.93% at 50% 50%,
    rgba(163, 84, 216, 0.55),
    rgba(102, 76, 43, 0)
  );
  box-shadow: rgba(203, 139, 79, 0.25) 0px 0.4vh 7.3vh;
  cursor: pointer;
  transition: all 0.2s ease-in 0s;
}
.main__corleone-inventory-left-grid-item-popup-btn:hover {
  box-shadow: rgba(146, 84, 216, 0.55) 0px 0px 1.7vh inset,
    rgba(203, 139, 79, 0.25) 0px 0.4vh 7.3vh;
}
.main__corleone-inventory-left-grid-item-popup-btn-2:hover {
  box-shadow: rgba(152, 84, 216, 0.55) 0px 0px 1.7vh inset,
    rgba(203, 139, 79, 0.25) 0px 0.4vh 7.3vh;
}
.main__corleone-inventory-left-grid-item-popup-btn-2 {
  padding: 0.5vh 2vh;
  font-size: 2.5vh;
  color: rgb(255, 255, 255);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25vh;
  gap: 1vh;
  background: radial-gradient(
    165% 163.93% at 50% 50%,
    rgba(203, 216, 84, 0.55),
    rgba(43, 102, 67, 0)
  );
  box-shadow: rgba(135, 203, 79, 0.25) 0px 0.4vh 7.3vh;
  transition: all 0.2s ease-in 0s;
  cursor: pointer;
}
.main__corleone-inventory-left-grid-item-popup-btn-3 {
  padding: 0.5vh 2vh;
  font-size: 2.5vh;
  color: rgb(255, 255, 255);
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  border-radius: 0.25vh;
  gap: 1vh;
  background: radial-gradient(
    165% 163.93% at 50% 50%,
    rgba(153, 3, 253, 0.604),
    rgba(102, 74, 43, 0)
  );
  box-shadow: rgba(182, 3, 253, 0.22) 0px 0.4vh 7.3vh;
  transition: all 0.2s ease-in 0s;
  cursor: pointer;
}
.main__corleone-inventory-left-grid-item-popup-btn-3:hover {
  box-shadow: rgb(140, 3, 253) 0px 0px 1.7vh inset;
}
.main__corleone-inventory-info-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-image: url("../images/inventory/info_bg.svg");
  background-size: cover;
  background-position: right center;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  width: 37.5vh;
  z-index: 2000;
  padding: 1vh;
  border-radius: 0.5vh;
}
.main__corleone-inventory-info-item-container {
  margin-top: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}
.main__corleone-inventory-info-item {
  width: 100%;
  padding: 0.25vh;
  background: radial-gradient(
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.05)
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.25vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1vh;
}
.main__corleone-inventory-info-item-left {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__corleone-inventory-info-icon {
  width: 3.5vh;
  height: 3.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2.5vh;
  color: rgb(255, 255, 255);
  border: 0.1vh solid rgb(140, 3, 253);
  background: linear-gradient(0deg, rgb(140, 0, 255), rgba(184, 28, 37, 0));
  box-shadow: rgb(140, 3, 253) 0px 0px 3.7vh inset,
    rgba(101, 28, 184, 0.55) 0px 0.4vh 3vh;
  border-radius: 0.25vh;
}
.main__corleone-inventory-info-text {
  font-size: 2.5vh;
  color: rgba(255, 255, 255, 0.5);
}
.main__corleone-inventory-info-text p span {
  color: rgb(255, 255, 255);
}
.main__corleone-inventory-info-item-right {
  font-size: 2.5vh;
  color: rgba(255, 255, 255, 0.5);
  margin-right: 1vh;
}
.main__corleone-inventory-info-search {
  height: 3vh;
  background: rgba(0, 0, 0, 0.6);
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  padding: 0.25vh 0.25vh 0.25vh 1vh;
  border-radius: 0.5vh;
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__corleone-inventory-info-search i {
  color: rgb(255, 255, 255);
  font-size: 1.25vh;
}
.main__corleone-inventory-info-search input {
  width: 10vh;
  font-family: var(--ff-body);
  color: rgb(255, 255, 255);
  padding-right: 0.5vh;
}
.main__corleone-inventory-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 5vh;
  color: rgba(255, 255, 255, 0.5);
}
.main__corleone-inventory-loader.small {
  font-size: 2.5vh;
  margin-top: -0.1vh;
}
.main__corleone-inventory-no-items {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.5vh;
  white-space: nowrap;
}
@keyframes zoomIn {
  0% {
    transform: scale(0.4);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes zoomInTransform {
  0% {
    transform: translate(-50%, -50%) scale(0.4);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}
.zoomIn {
  animation: 0.25s cubic-bezier(0.075, 0.82, 0.165, 1) 0s 1 normal none running
    zoomIn;
}
.zoomInTransform {
  animation: 0.25s cubic-bezier(0.075, 0.82, 0.165, 1) 0s 1 normal none running
    zoomInTransform;
}

.react-draggable {
  transition: all 0.2s ease;
}

.react-draggable.dragging {
  z-index: 9999 !important;
  opacity: 0.8;
  cursor: move !important;
  transition: none !important;
}

.react-draggable:hover {
  cursor: move;
}

.drag-mode .react-draggable {
  border: 2px dashed #00ff00 !important;
  cursor: move !important;
}

.drag-mode .react-draggable:hover {
  border-color: #00aa00 !important;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.drag-mode .main__final-carhud-container {
  display: block !important;
  opacity: 1 !important;
}
