fx_version 'cerulean'
game 'gta5'
lua54 'yes'
version '1.0.0'


shared_script '@es_extended/imports.lua'

client_scripts {
    'client/client.lua',
    'client/notify.lua'
}

server_scripts {
    'server/server.lua',
    'server/notify.lua'
}

ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/index.css',
    'html/config.css',
    'html/hud.js',
    'html/fonts/**/*',
    'html/images/**/*'
}

dependencies {
    'es_extended'
}

escrow_ignore {
    'server/server.lua',
    'server/notify.lua',
}
dependency '/assetpacks'