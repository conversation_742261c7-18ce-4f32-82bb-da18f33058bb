<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://kit.fontawesome.com/96a2c6323b.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="css/style.css">
    <title>by cscripts</title>
</head>
<body style="background-color: grey;">
    <script src="./js/jquery-3.6.0.js"></script>
    <script src="./js/jquery-ui.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="js/script.js" crossorigin="anonymous"></script>
    <style>
        body {
            font-size: 2vh;
            color: var(--clr-white);
            font-weight: 600;
            font-family: var(--ff-special);
            overflow: hidden;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        img {
            width: 100%;
        }

        .main__craft-container,
        .main__gunshop-container {
            width: 100vw;
            height: 100vh;
            background: radial-gradient(50% 50% at 50% 50%, rgba(71, 38, 38, 0.25) 0%, rgb(67, 33, 33)100%);
            overflow: hidden;
        }

        .main__garage-container {
            position: relative;
            margin: 0 auto;
            margin-top: 19vh;
            width: 90vh;
            height: 62vh;
            border-radius: 1vh;
            background: url(img/garage/bg.png);
            background-size: cover;
            background-position: center;
            overflow: hidden;
        }

        .main__identity-container {
            position: absolute;
            top: 0vh;
            left: 0vh;
            width: 100%;
            height: 100vh;
            background-position: center;
            background-size: cover
        }

        .main__identity-bg {
            position: absolute;
            top: 0vh;
            left: 0vh;
            width: 100%;
            height: 100vh;
            background: url(img/identity/bg_3.png);
            background-size: cover;
            background-position: center
        }

        .main__fuel-container-item {
            position: relative;
            height: 55vh;
            width: 42vh;
            padding: 4vh;
            border-radius: var(--border-radius-frame);
            background-position: center;
            overflow: hidden;
            background: url(img/fuel/bg.png);
            background-size: cover;
            top: 50%;
            left: 15%;
        }

        .main__driving-container-1 {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100vh;
            height: 72vh;
            padding: 3vh;
            border-radius: var(--border-radius-frame);
            background: url(img/driving/bg.png);
            background-size: cover;
            overflow: hidden;
        }

        .main__fuel-job-container {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            left: 50%;
            transform: translateX(-50%);
            flex-direction: column;
            top: 5vh;
            width: 20vh;
        }

        .main__fuel-liter-header {
            text-transform: uppercase;
            color: var(--clr-white);
            text-shadow: 0vh 0vh 1vh rgba(255, 255, 255, .5);
            font-size: 1.5vh;
            font-weight: 600;
            margin-bottom: 1vh;
        }

        .main__fuel-job-progress {
            width: 100%;
            height: .35vh;
            background: rgba(0, 0, 0, .5);
        }

        .main__fuelinfo-container {
            position: absolute;
            left: 50%;
            transform: translate(-50%) scale(0.8);
            bottom: -2vh;
            width: 35vh;
        }

        .main__char-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .main__housing-container-1 {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: radial-gradient(49.48% 156.38% at 50% 50%, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
        }

        .main__housing-container-2,
        .main__housing-container-3 {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: radial-gradient(49.48% 156.38% at 50% 50%, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
        }

        .main__content-header {
            margin-left: 15vh;
        }

        .main__content-header-img {
            position: absolute;
        }

        .main__content-header-img img {
            width: 40vh;
        }

        .main__content-header-small-text {
            font-size: 2.5vh;
            text-transform: uppercase;
            font-weight: 500;
            color: #FFFFFF75;
        }

        .main__content-header-big-text {
            font-size: 7.2vh;
            text-transform: uppercase;
            font-weight: 700;
            color: var(--clr-white);
            margin-top: -1vh;
        }

        .main__content-header-text {
            position: absolute;
            top: 3.5vh;
            left: 16.5vh;
        }

        .main__content-header-sphere-6 {
            position: absolute;
            right: 2vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(255, 0, 0, 0.25);
            filter: blur(15vh);
        }

        .main__content-header-sphere-5 {
            position: absolute;
            left: 40vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: #FFFFFF40;
            filter: blur(15vh);
        }

        .main__content-header-sphere-4 {
            position: absolute;
            left: 25vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(215, 132, 56, 0.25);
            filter: blur(15vh);
        }

        .main__content-header-sphere-3 {
            position: absolute;
            left: 20vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(95, 35, 138, 0.539);
            filter: blur(15vh);
        }

        .main__content-header-sphere-2 {
            position: absolute;
            left: 22.5vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(95, 35, 138, 0.539);
            filter: blur(15vh);
        }

        .main__content-header-sphere-1 {
            position: absolute;
            left: 5vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(215, 56, 142, 0.25);
            filter: blur(15vh);
        }

        .main__garage-header {
            margin-left: 1vh;
            margin-top: 1.5vh;
        }

        .main__garage-header-left-stripe {
            width: 0.1vh;
            height: 3.5vh;
            background: rgba(255, 255, 255, 0.5);
            margin-right: 0.5vh;
        }

        .main__garage-header-left {
            font-family: var(--ff-arame);
            font-size: 1.5vh;
        }

        .main__garage-header-left p:first-child {
            font-weight: 100;
            color: #ffffff80;
            letter-spacing: 0.5vh;
            line-height: 1.5vh;
        }

        .main__garage-header-left P:last-child {
            font-weight: 700;
            font-size: 2.6vh;
            color: #fff;
            line-height: 2.6vh;
        }

        .main__garage-header-bg img {
            width: 6vh;
        }

        .main__garage-info-container {
            position:absolute;
            top:3.25vh;
            right:8vh;
            display:flex;
            gap:1vh
        }

        .main__garage-info-grid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
        }

        .main__garage-info-button-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__garage-info-button#parkIn {
            display: flex;
            justify-content: center;
            padding: 1vh;
            align-items: center;
            border-radius: 0.4vh;
            height: 3.5vh;
            border: 0.1vh solid #c11717;
            background: linear-gradient(0deg, #9c1414 0%, rgba(255, 125, 69, 0) 100%);
            box-shadow: 0 0 3.7vh #a30909 inset, 0 0.4vh 5.6vh #ff454540;
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__garage-info-button#parkIn:hover,
        .main__garage-info-button#parkIn.active {
            box-shadow: 0 0 1.7vh #9c1414 inset;
        }

        .main__garage-info-button#parkOut {
            height: 3.5vh;
            padding: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.4vh;
            border: 0.1vh solid #d939d9;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(190, 57, 217, 0.25) 0%, rgba(206, 57, 217, 0) 100%);
            box-shadow: 0 0 3.7vh #d939d9 inset, 0 0.4vh 5.6vh #b439d940;
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
            transition: 0.2s ease-in;
            cursor: pointer;
        }

        .main__garage-info-button#parkOut:hover,
        .main__garage-info-button#parkOut.active {
            box-shadow: 0 0 1.7vh #d939d9 inset;
        }

        .main__garage-info-button#parkFavOut {
            position: relative;
            height: 3.5vh;
            padding-left: 1vh;
            padding-right: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.4vh;
            border: 0.1vh solid #ff4848;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(217, 57, 57, 0.25) 0%, rrgba(217, 57, 57, 0)100%);
            box-shadow: 0 0 3.7vh #ff4545 inset, 0 0.4vh 5.6vh #d9393940;
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
            transition: 0.2s ease-in;
            cursor: pointer;
            gap: 0.5vh;
            overflow: hidden;
        }

        .main__garage-info-button#parkFavOut p {
            z-index: 100;
        }

        .main__garage-info-button#parkFavOut:hover .main__garage-info-button-bg,
        .main__garage-info-button#parkFavOut.active .main__garage-info-button-bg {
            transform: translate(-50%, -50%) scale(2);
            color: #8f1e1e;
        }

        .main__garage-info-button-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 3vh;
            color: #741919;
            transition: 0.2s ease-in;
            z-index: 0;
            opacity: 0.25;
        }

        .main__garage-exit-button-container {
            position: absolute;
            top: 3.2vh;
            right: 3.5vh;
        }

        .main__garage-exit-button-grid {
            display: flex;
            gap: 1vh;
            align-items: center;
        }

        .main__garage-exit-circle {
            width: 3.5vh;
            height: 3.5vh;
            text-transform: uppercase;
            font-weight: 400;

            border-radius: 0.5vh;
            border: .1vh solid #FF3055;
            background: linear-gradient(0deg,#FF0935 0%,rgba(184,28,37,0) 100%);
            box-shadow: 0 0 3.7vh #ee4b69 inset,0 .4vh 3vh #b81c258c;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.5vh;
            cursor: pointer;
            transition: .2s ease-in;
            position: relative;
            z-index: 9999
        }

        .main__garage-exit-circle i {
            transition: .2s ease-in
        }

        .main__garage-exit-circle:hover {
            opacity: .75;
            box-shadow: 0 0 3.7vh #ee4b69 inset,0 .4vh 5vh #ff1723
        }

        .main__garage-exit-circle:hover i {
            transform: rotate(90deg)
        }

        .main__garage-scroll-container {
            width: 100vh;
            height: 52vh;
            position: absolute;
            left: 52vh;
            transform: translateX(-50%);
            top: 85px;
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            align-content: flex-start;
            gap: 1vh;
            justify-content: flex-start;
            overflow-y: auto;
            overflow-x: hidden;
        }
            
        .main__scroll-item-button {
            position:absolute;
            width:17.8vh;
            height:3.8vh;
            margin-top: 17.5vh;
            margin-left: 0.4vh;
            display:flex;
            justify-content:center;
            align-items:center;
            text-transform:uppercase;
            font-size:1.2vh;
            color:var(--color-white);
            border-radius:.25vh;
            border:.1vh solid rgba(255,255,255,.15);
            box-shadow:0 0 3.7vh #ffffff26 inset,0 .4vh 5.6vh #ffffff26;
            padding:1vh;
            cursor:pointer;
            transition:border .2s ease-in,box-shadow .2s ease-in,background-color .2s ease-in
        }
            
        .main__scroll-item-button.notallowed {
            cursor: not-allowed;
        }
            
        .main__scroll-item-button:hover {
            box-shadow:0 0 3vh #fff inset,0 0 1.7vh #c11717;
            background:#9c1414;
            border:.1vh solid transparent
        }

        .main__garage-scroll-item {
            position: relative;
            width: 225px;
            height: 260px;
            padding: 1vh;
            transition: .2s ease-in;
        
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0 0 2.5vh #ffffff26 inset;
            border-radius: .4vh;
        }
        
        .garage-item {
            transform: translateY(0);
            opacity: 1;
            transition: transform 0.4s ease-out, opacity 0.4s ease-out;
        }
        
        .garage-item.animate-in {
            transform: translateY(20px);
            opacity: 0;
        }
            
        .main__garage-scroll-item-top-header {
            position: absolute;
            top: 1.5vh;
            right: 1.5vh;
            font-size: 1.8vh;
            display: flex;
            align-items: center;
            gap: .5vh;
            text-transform: uppercase;
            font-weight: 400;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__garage-scroll-item-top-header i {
            color: #ffffff40;
            cursor: pointer;
            transition: 0.2s ease-in;
            font-size: 1.5vh;
        }

        .main__garage-scroll-item-top-header:hover i,
        .main__garage-scroll-item-top-header.active i {
            color: #ff4848;
            text-shadow: 0vh 0vh 1.7vh #ff4848;
        }
            
        .main__garage-scroll-item-bottom-container {
            position: absolute;
            top: 1vh;
            left: 1vh;
            width: 100%;
        }
            
        .main__garage-scroll-item-bottom-container-header {
            position: absolute;
            font-size:2vh;
            font-family:var(--ff-druk);
            text-transform:uppercase;
            background:radial-gradient(539.11% 270.5% at 50.17% 50%,#c11717 0%,rgba(217,174,57,0) 100%);
            background-clip:text;
            -webkit-background-clip:text;
            -webkit-text-fill-color:transparent
        }
            
        .main__garage-scroll-item-bottom-container-description {
            position: absolute;
            font-family: 'Rajdhani';
            font-style: normal;
            font-weight: 400;
            font-size:1.2vh;
            line-height: 19px;
            color:var(--color-white);
            margin-top: 2.5vh;
        }
            
        .main__garage-scroll-item-car-img {
            position: absolute;
            top:50%;
            left:50%;
            transform: translate(-50%, -50%);
            src: url('./img/garage/panto.png');
        }
            
        .main__garage-scroll-item-car-img img {
            src: url('./img/garage/panto.png');
            width:15vh
        }
            
        .garage__span-1 {
            color:#ffcc48 !important;
            text-shadow: 0vh 0vh 1.7vh #ffcc48 !important;
        }
            
        .garage__span-2 {
            color:#4c48ff !important;
            text-shadow: 0vh 0vh 1.7vh #4c48ff !important;
        }
            
        .garage__span-3 {
            color: #cf48ff !important;
            text-shadow: 0vh 0vh 1.7vh #cf48ff !important;
        }

        .main__garage-rename-icon {
            position: absolute;
            top: 0.9vh;
            right: 4vh;
            cursor: pointer;
            transition: .2s ease-in;
            z-index: 1;
        }

        .main__garage-rename-icon i {
            color: #ffffff40;
            cursor: pointer;
            transition: 0.2s ease-in;
            font-size: 1.5vh;
        }
        .main__garage-rename-icon:hover i,
        .main__garage-rename-icon.active i {
            color: #ff4848;
            text-shadow: 0vh 0vh 1.7vh #ff4848;
        }

        .main__garage-scroll-item-rename-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            margin-top: 2vh;
        }
        
        .main__garage-scroll-item-rename-header-icon {
            font-size: 3.25vh;
            text-transform: uppercase;
            color: var(--clr-new-orange);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
        }
        
        .main__garage-scroll-item-rename-header-small {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: rgba(255, 255, 255, .55);
            font-family: var(--ff-inter);
            font-weight: 300;
            margin-top: 1vh;
        }
        
        .main__garage-scroll-item-rename-input-container {
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 16vh;
            background: rgba(255, 255, 255, .05);
            border-radius: .8vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1vh;
            margin-top: 2.75vh;
        }
        
        .main__garage-scroll-item-rename-input-container input {
            background: none;
            outline: none;
            border: none;
            width: 10vh;
            font-family: var(--ff-inter);
            font-size: 1.3vh;
            text-align: center;
            color: rgba(255, 255, 255, .25);
        }
        
        .main__garage-scroll-item-rename-input-container input::placeholder {
            color: rgba(255, 255, 255, .25);
        }

        .main__garage-item-window-2,
        .main__garage-item-window-1 {
            position: relative;
            height: 100%;
        }
        
        .main__garage-scroll-item-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            bottom: -6vh;
            left: 0.5vh;
            width: 17.8vh;
            height: 3.8vh;
            text-transform: uppercase;
            font-family: var(--ff-rad);
            font-size: 1.2vh;
            color: var(--color-white);
            border-radius: .25vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 3.7vh 0vh rgba(255, 255, 255, .15) inset, 0vh .4vh 5.6vh 0vh rgba(255, 255, 255, .15);
            padding: 1vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__garage-scroll-item-btn:hover {
            box-shadow: 0vh 0vh 3vh #ffffff inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }

        .main__garage-scroll-item-return-icon {
            position: absolute;
            top: -1.5vh;
            right: 0.5vh;
            color: rgba(255, 255, 255, .25);
            cursor: pointer;
            font-size: 1.6vh;
            transition: .2s ease-in;
        }
        
        .main__garage-scroll-item-return-icon:hover {
            color: var(--clr-new-orange);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
        }

        .main__shop-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 125vh;
            padding: 3vh;
            border-radius: var(--border-radius-frame);
            background: url(img/shop/bg.png);
            background-size: cover;
            background-position: center;
            overflow: hidden;
        }
        
        .main__shop-grid-container {
            position: relative;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr .35fr;
            gap: 2vh;
            margin-top: 2vh;
        }
        
        .main__shop-grid-left-container {
            position: relative;
            width: 100%;
        }
        
        .main__shop-grid-left-scroll-container {
            position: relative;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1vh;
            height: 51vh;
            overflow-y: scroll;
            padding-right: 1vh;
        }
        
        .main__shop-grid-left-scroll-container::-webkit-scrollbar,
        .main__shop-grid-right-scroll-container::-webkit-scrollbar {
            width: .3vh;
        }
        
        .main__shop-grid-left-scroll-container::-webkit-scrollbar-track,
        .main__shop-grid-right-scroll-container::-webkit-scrollbar-track {
            width: .3vh;
            background: rgba(255, 255, 255, .05);
        }
        
        .main__shop-grid-left-scroll-container::-webkit-scrollbar-thumb,
        .main__shop-grid-right-scroll-container::-webkit-scrollbar-thumb {
            width: .3vh;
            background: #c11717;
            box-shadow: 0vh 0vh 1.7vh #c11717;
        }
        
        .main__shop-grid-left-scroll-item {
            width: 100%;
            position: relative;
            height: 25vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0vh 0vh 1.7vh #ffffff26 inset;
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            padding: 1.5vh;
        }
        
        .main__shop-grid-left-scroll-item-header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .main__genisis-weapon-bottom-weapon-btn-container {
            width: 100%;
            margin-top: 1vh;
        }
        
        .main__weapon-bottom-weapon-btn-buy,
        .main__genisis-weapon-bottom-weapon-btn-price {
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
        }
        
        .main__weapon-bottom-weapon-btn-buy {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .5vh;
            border-radius: .25vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__weapon-bottom-weapon-btn-buy:hover {
            box-shadow: 0vh 0vh 3vh #382e03 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__genisis-weapon-bottom-weapon-btn-price {
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0px 0px 37px 0px #ff4141 inset;
            padding: .5vh;
            padding-top: .25vh;
            padding-bottom: .25vh;
            border-radius: .25vh;
            border: .1vh solid #ff4141;
        }
        
        .main__shop-grid-left-scroll-item-img {
            position: absolute;
            width: 12vh;
            height: 12vh;
            display: flex;
            justify-content: center;
            align-items: center;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            margin-top: 0vh;
        }
        
        .main__shop-grid-left-scroll-item-img img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .main__shop-grid-right-scroll-container {
            width: 100%;
            display: grid;
            grid-template-columns: 1fr;
            align-items: flex-start;
            align-content: flex-start;
            padding-right: 1vh;
            gap: .5vh;
            height: 39.5vh;
            overflow-y: scroll;
        }
        
        .main__shop-grid-right-scroll-item {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0vh 0vh 1.7vh #ffffff26 inset;
            color: var(--color-white);
            padding: 1vh;
            padding-top: .5vh;
            padding-bottom: .5vh;
        }
        
        .main__shop-grid-right-scroll-item-img {
            width: 4vh;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .main__shop-grid-right-scroll-item-img img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .main__shop-grid-right-scroll-item-information {
            font-size: 1.4vh;
        }
        
        .main__shop-grid-right-scroll-item-information p:nth-last-child(1) {
            font-size: 1.1vh !important;
            color: #ffffff80 !important;
        }
        
        .main__shop-grid-right-scroll-item-price {
            font-size: 1.2vh;
            box-shadow: 0vh 0vh 3.7vh 0vh #ff4141 inset;
            padding: .65vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .05);
            width: 7vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .main__shop-grid-right-scroll-item-remove {
            padding: .65vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            font-size: 1.2vh;
            cursor: pointer;
            transition: .2s ease-in;
            width: 2.65vh;
        }
        
        .main__shop-grid-right-scroll-item-remove i {
            transition: .2s ease-in;
        }
        
        .main__shop-grid-right-scroll-item-remove:hover {
            box-shadow: 0vh 0vh 2.5vh rgba(255, 255, 255, .25) inset, 0vh 0vh 1.7vh rgba(255, 255, 255, .15);
        }
        
        .main__shop-grid-right-scroll-item-remove:hover i {
            transform: rotateY(180deg);
        }
        
        .main__shop-grid-right-scroll-item-flex {
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        
        .main__shop-grid-right-price-information-container {
            margin-top: 5.5vh;
        }
        
        .main__shop-grid-right-price-information-text-container {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            text-transform: uppercase;
            font-size: 1.5vh;
            color: var(--color-white);
        }
        
        .main__shop-grid-right-price-information-text-container p:nth-last-child(1) {
            color: #ff4141;
            text-shadow: 0vh 0vh 1.7vh #ff4141;
        }
        
        .main__shop-grid-right-price-information-btn-container {
            width: 100%;
            margin-top: 1vh;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
        }
        
        .main__shop-grid-right-price-information-btn {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: .5vh;
            padding: .65vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            cursor: pointer;
            transition: .2s ease-in;
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
        }
        
        .main__shop-grid-right-price-information-btn:hover {
            box-shadow: 0vh 0vh 3vh #382e03 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }

        .main__salty-bg-container {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
        }

        .main__salty-bg-1 {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 70vh;
            height: 100vh;
            background: linear-gradient(180deg, rgba(91, 222, 220, 0.08) 0%, rgba(63, 217, 215, 0) 87.18%)
        }

        .main__salty-bg-2 {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 50vh;
            height: 100vh;
            background: linear-gradient(180deg, rgba(91, 222, 220, 0.08) 0%, rgba(63, 217, 215, 0) 87.18%)
        }

        .main__salty-bg-3 {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 20vh;
            height: 100vh;
            background: linear-gradient(180deg, rgba(91, 222, 220, 0.08) 0%, rgba(63, 217, 215, 0) 87.18%)
        }

        .main__identity-bg-container {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
        }

        .main__identity-bg-1 {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 70vh;
            height: 100vh;
            background: linear-gradient(180deg, rgba(91, 222, 220, 0.08) 0%, rgba(63, 217, 215, 0) 87.18%)
        }

        .main__identity-bg-2 {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 50vh;
            height: 100vh;
            background: linear-gradient(180deg, rgba(91, 222, 220, 0.08) 0%, rgba(63, 217, 215, 0) 87.18%)
        }

        .main__identity-bg-3 {
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 20vh;
            height: 100vh;
            background: linear-gradient(180deg, rgba(91, 222, 220, 0.08) 0%, rgba(63, 217, 215, 0) 87.18%)
        }

        .main__identity-center-container {
            width: 33vh;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__identity-header {
            position:absolute;
            top:4.5%;
            left:50%;
            transform:translate(-50%,-50%);
            width:40vh
        }

        .main__identity-header-logo {
            width:10vh;
            height:10vh;
            position:relative;
            left:50%;
            transform:translate(-50%);
            margin-bottom:1vh;
            display:flex;
            justify-content:center;
            align-items:center;
            animation:bounce-wobble-register 2s infinite
        }

        .main__identity-header img {
            width:100%
        }

        @keyframes bounce-wobble-register {
            0%,to {
                transform:translateY(0) rotate(0) translate(-50%)
            }
            25% {
                transform:translateY(-15px) rotate(5deg) translate(-50%)
            }
            50% {
                transform:translateY(0) rotate(-5deg) translate(-50%)
            }
            75% {
                transform:translateY(-7.5px) rotate(3deg) translate(-50%)
            }
        }

        .main__identity-small-header {
            text-align: center;
            font-family: Arame-Mono;
            font-size: 1.5vh;
            color: #ffffff80;
            letter-spacing:.5vh
        }

        .main__identity-big-header {
            text-align:center;
            font-family: Arame-Mono;
            font-size:3vh;
            color:#fff
        }

        .main__identity-item-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: 1.5vh;
            margin-top: 12vh;
        }

        .main__identity-item {
            position: relative;
            width: 100%;
            padding: 1vh;
            border: .1vh solid rgba(255,255,255,.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            border-radius: .5vh;
            display: flex;
            align-items: center;
        }

        .main__identity-item-geschlecht {
            display:flex;
            justify-content:center;
            align-items:center;
            width:112%;
            padding:2vh;
            margin-top: -3.5vh;
        }

        .main__identity-item-left-icon {
            width:4vh;
            height:4vh;
            padding:.5vh;
            border:.1vh solid rgba(255,255,255,.15);
            box-shadow:0 0 1.7vh #ffffff26 inset;
            border-radius:.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: -0.8vh;
        }

        .main__identity-item-left-icon#vorname,
        .main__identity-item-left-icon#nachname {
            margin-left: -4vh;
        }

        .main__identity-item-left-icon#referral,
        .main__identity-item-left-icon#geburtsdatum {
            margin-left: -4vh;
        }

        .main__identity-item-left-icon img {
            width: 3vh
        }

        .main__identity-item-header {
            font-weight: 400;
            font-size:1.25vh;
            color:#fff
        }

        .main__identity-item-header#titleHeight {
            color: #ffffff;
            font-weight: 600;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__identity-item-input {
            font-family: var(--ff-special);
            font-size: 1.7vh;
            background: none;
            border: none;
            outline: none;
            color: #fff;
            font-weight: 400;
            width: 31vh;
            padding-bottom: .5vh;
            border-bottom: .1vh solid rgba(255,255,255,.15);
        }

        .main__identity-item-input::placeholder {
            color:#ffffff80
        }

        .main__identity-btn-gird {
            display:grid;
            grid-template-columns:repeat(2,1fr);
            gap:1vh;
            margin-top: -1.5vh;
        }

        .main__identity-big-button {
            width: 100%;
            padding: 2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 400;
            cursor: not-allowed;
            z-index: 100;
            margin-top:1vh;
            border-radius:.5vh;
            border:.1vh solid rgba(255,255,255,.15);
            box-shadow:0 0 1.7vh #ffffff26 inset;
            font-size:1.25vh;
            color:#fff;
            transition:.2s ease-in;
            opacity:.5
        }

        .main__identity-big-button.active {
            background: #9575ac30;
            cursor: pointer;
            opacity: 1;
        }

        .main__identity-big-button:hover {
            box-shadow:0 0 3vh #380303 inset,0 0 1.7vh #c11717;
            background:#9c1414;
            border:.1vh solid transparent
        }

        .main__identity-small-button:hover {
            box-shadow:0 0 3vh #791515 inset,0 0 1.7vh #fd1e1e;
            background:#923232;
            border:.1vh solid transparent
        }

        .main__identity-small-button {
            width: 100%;
            font-weight: 400;
            margin-top: 1vh;
            z-index: 100;
            display:flex;
            justify-content:center;
            align-items:center;
            padding:2vh;
            border-radius:.5vh;
            border:.1vh solid rgba(255,255,255,.15);
            box-shadow:0 0 1.7vh #ffffff26 inset;
            font-size:1.25vh;
            color: #fff;
            cursor:pointer;
            transition: .2s ease-in;
        }

        .main__identity-item-input::-webkit-calendar-picker-indicator {
            opacity: 0;
        }

        .main__identity-item-grid {
            margin-top:1vh;
            width:100%;
            display:grid;
            grid-template-columns:repeat(2,1fr);
            gap:1vh
        }

        .main__identity-item-button {
            width: 100%;
            padding: 1.7vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            color: #fff;
            border: .1vh solid rgba(255,255,255,.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            border-radius: .5vh;
            cursor: pointer;
            transition: .2s ease-in
        }

        .main__identity-item-button:hover,
        .main__identity-item-button.active {
            box-shadow:0 0 3vh #380303 inset,0 0 1.7vh #c11717;
            background:#9c1414;
            border:.1vh solid transparent
        }

        .main__identity-right-container {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 39vh;
            display: flex;
            flex-direction: column;
            gap: 1vh;
        }

        .main__identity-right-item {
            width: 100%;
            padding: 1.5vh;
            background: linear-gradient(90deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 105.43%);
            border: 1px solid;
            border-image-source: linear-gradient(90.11deg, rgba(95, 35, 138, 0.539) -0.65%, rgba(95, 35, 138, 0.539) 103.3%);
            border-image-slice: 1;
            padding-left: 4vh;
            padding-right: 4vh;
        }

        .main__identity-right-item-header {
            text-align: center;
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #ffffff;
            text-shadow: 0vh 0vh 1.7vh #9575ac30;
        }

        .main__identity-right-item-text {
            text-align: center;
            font-size: 1.25vh;
            color: #ffffff;
        }

        .main__identity-item-input.wrong {
            color: #FC4E51;
        }

        .main__identity-item-input.success {
            color: #f7f5f8d8;
        }

        .main__identity-big-grid-item-header {
            margin-top: 11vh;
            font-size: 2.4vh;
            text-transform: uppercase;
            color: #7331a3;
            text-shadow: 0vh 0vh 1.7vh #7331a3;
            margin-bottom: 2vh;
        }

        .main__identity-big-small-grid {
            width: 100%;
            display: grid;
            grid-template-columns: .3fr 1fr;
            gap: 2vh;
        }

        .main__identity-big-small-circle {
            width: 100%;
            height: 7vh;
            border: .3vh solid #7331a3;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.4vh;
            text-transform: uppercase;
            font-weight: 500;
            color: #7331a3;
            box-shadow: 0vh 0vh 1.7vh #7331a3;
            text-shadow: 0vh 0vh 1.7vh #7331a3;
        }

        .main__identity-big-small-information-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #7331a3;
            text-shadow: 0vh 0vh 1.7vh #7331a3;
            width: 100%;
            display: flex;
            justify-content: center;
            flex-direction: column;
        }

        .main__identity-big-small-information-text {
            font-size: 1.4vh;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.5);
        }

        .main__housing-center-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40vh;
        }

        .main__housing-header {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .main__housing-stripes {
            position: absolute;
            z-index: -1;
        }

        .main__housing-stripes img {
            width: 30vh;
        }

        .main__housing-house {
            position: absolute;
            z-index: 1;
            margin-top: 2vh;
        }

        .main__housing-house-2 {
            position: absolute;
            z-index: 1;
            margin-top: 2vh;
            margin-left: -6vh;
        }

        .main__housing-house-2 img {
            width: 30vh;
        }

        .main__housing-house img {
            width: 30vh;
        }

        .main__housing-clothes {
            position: absolute;
            z-index: 1;
            margin-left: 28vh;
            margin-top: 2vh;
        }

        .main__housing-clothes img {
            width: 12vh;
        }

        .main__housing-tee {
            position: absolute;
            z-index: 1;
            margin-left: 15vh;
            margin-top: -2vh;
        }

        .main__housing-tee img {
            width: 20vh;
        }

        .main__housing-text {
            position: absolute;
            z-index: 2;
            margin-top: 13vh;
        }

        .main__housing-text img {
            width: 78vh;
        }

        .main__housing-settings {
            position: absolute;
            z-index: 1;
            margin-left: 25vh;
            margin-top: 4vh;
        }

        .main__housing-settings img {
            width: 12vh;
        }

        .main__housing-information-grid {
            width: 100%;
            display: grid;
            grid-template-columns: .2fr 1fr;
            gap: 1vh;
            margin-top: 12vh;
        }

        .main__housing-icon-container {
            width: 100%;
            height: 7.5vh;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            padding: 2vh;
            display: flex;
            justify-content: center;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;

            align-items: center;
        }

        .main__housing-icon-container img {
            width: 3vh;
        }

        .main__housing-icon-point {
            width: 2vh !important;
        }

        .main__housing-information-container {
            position: relative;
            width: 100%;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            height: 7.5vh;
            display: flex;
            justify-content: center;
            flex-direction: column;
            padding-left: 2vh;
        }

        .main__housing-information-header {
            font-size: 1.4vh;
            color: #38D6D48C;
            font-weight: 400;
        }

        .main__housing-information-content {
            font-size: 1.8vh;
            color: #ffffff;
        }

        .main__housing-information-content-price {
            font-size: 1.8vh;
            color: #ffffff;
        }

        .main__housing-big-button {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: .2s ease-in;
            font-size: 1.4vh;
            padding: 2vh;
            background: #9575ac30;
            color: #28738A;
            margin-top: 1vh;
            text-transform: uppercase;
            font-weight: 400;
        }

        .main__housing-big-button:hover {
            box-shadow: 0vh 0vh 1.7vh #9575ac30;
        }

        .main__housing-small-button {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: .2s ease-in;
            font-size: 1.4vh;
            padding: 1vh;
            background: #9575ac301F;
            color: #28738A;
            margin-top: .5vh;
            text-transform: uppercase;
            font-weight: 400;
        }

        .main__housing-small-button.disabled {
            cursor: not-allowed;
        }

        .main__housing-small-button:hover {
            box-shadow: 0vh 0vh 1.7vh #9575ac301F;
        }

        .main__housing-corner-left,
        .main__craft-corner-left {
            position: absolute;
            top: .1vh;
            left: .1vh;
        }

        .main__housing-corner-right,
        .main__craft-corner-right {
            position: absolute;
            top: .1vh;
            right: .1vh;
        }

        .main__housing-corner-left,
        .main__housing-corner-right,
        .main__craft-corner-right,
        .main__craft-corner-left {
            width: .4vh;
            height: .4vh;
        }

        .main__housing-2-item-container {
            margin-top: 12vh;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 1vh;
        }

        .main__housing-2-item {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 7.5vh;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            font-size: 1.8vh;
            color: #38D6D4;
        }

        .main__housing-outfit-wrap {
            position: relative;
            width: 100%;
            height: 25.5vh;
        }

        .main__housing-outfit-container {
            position: absolute;
            z-index: 5;
            margin-top: 12vh;
            width: 100%;
            height: 25.5vh;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-content: flex-start;
            overflow-y: scroll;
            gap: 1vh;
        }

        .main__housing-outfit-container::-webkit-scrollbar {
            width: 0vh;
        }

        .main__housing-outfit {
            position: relative;
            width: 100%;
            height: 7.5vh;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.8vh;
            color: #9575ac30;
        }

        .main__housing-outfit:hover {
            background: rgba(69, 191, 58, 0.1);
        }

        .main__salty-center-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 49vh;
            padding: 3vh;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            border: .17vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            animation: 5s saltymove infinite;
        }

        @keyframes saltymove {
            0% {
                margin-top: 0vh;
            }

            30% {
                margin-top: 7.5vh;
            }

            80% {
                margin-top: -7.5vh;
            }

            100% {
                margin-top: 0vh;
            }
        }

        .main__salty-information-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.4vh;
            text-transform: uppercase;
            background: linear-gradient(91.08deg, #ffffff 0.4%, #ffffff 99.36%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }

        .main__salty-information-grid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
            margin-top: 2vh;
        }

        .main__salty-information-grid-item {
            position: relative;
            width: 100%;
            height: 21.5vh;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            padding: .5vh;
        }

        .main__salty-information-grid-item-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            font-size: 1.4vh;
            text-transform: uppercase;
            padding: .25vh;
        }

        .main__salty-information-grid-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__salty-information-grid-logo img {
            width: 20vh;
        }

        .main__salty-information-grid-item-bottom {
            position: absolute;
            bottom: .5vh;
            left: .5vh;
            width: 95%;
            background: #9575ac30;
            padding: .25vh;
            font-size: 1.4vh;
            text-transform: uppercase;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 400;
            color: #fffffff;
        }

        .main__salty-bottom-information-container {
            width: 100%;
            margin-top: 1.25vh;
        }

        .main__salty-bottom-information-grid {
            width: 100%;
            display: grid;
            grid-template-columns: .2fr 1fr;
            gap: 1vh;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            padding: 1vh;
        }

        .main__salty-bottom-information-grid-header {
            font-size: 1.6vh;
            text-transform: uppercase;
            color: #D78438;
            text-shadow: 0vh 0vh 1.7vh #D78438;
        }

        .main__salty-bottom-information-grid-text {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: #FFFFFF8C;
            font-weight: 500;
        }

        .main__salty-bottom-information-grid-icon {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__salty-bottom-information-grid-icon img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 7.5vh;
        }


        .main__content-header {
            margin-left: 5vh;
        }

        .main__content-header-img {
            position: absolute;
        }

        .main__content-header-img img {
            width: 40vh;
        }

        .main__content-header-small-text {
            font-size: 2.5vh;
            text-transform: uppercase;
            font-weight: 500;
            color: #FFFFFF75;
            margin-left: -.75vh;
            margin-top: 2vh;
        }

        .main__content-header-big-text {
            font-size: 5vh;
            text-transform: uppercase;
            font-weight: 700;
            color: var(--clr-white);
            margin-top: -1vh;
        }

        .main__content-header-text {
            position: absolute;
            top: 3.5vh;
            left: 7.3vh;
        }

        .main__content-header-sphere-6 {
            position: absolute;
            right: 2vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(255, 0, 0, 0.25);
            filter: blur(15vh);
        }

        .main__content-header-sphere-5 {
            position: absolute;
            left: 40vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: #FFFFFF40;
            filter: blur(15vh);
        }

        .main__content-header-sphere-4 {
            position: absolute;
            left: 25vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(215, 132, 56, 0.25);
            filter: blur(15vh);
        }

        .main__content-header-sphere-3 {
            position: absolute;
            left: 20vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(95, 35, 138, 0.539);
            filter: blur(15vh);
        }

        .main__content-header-sphere-2 {
            position: absolute;
            left: 22.5vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(95, 35, 138, 0.539);
            filter: blur(15vh);
        }

        .main__content-header-sphere-1 {
            position: absolute;
            left: 5vh;
            top: -18vh;
            width: 21vh;
            height: 21vh;
            border-radius: 50%;
            background: rgba(215, 56, 142, 0.25);
            filter: blur(15vh);
        }

        .main__craft-left-container {
            position: absolute;
            left: 5vh;
            top: 15vh;
            width: 41.7vh;
            height: 80vh;
        }

        .main__craft-left-search-bar {
            width: 100%;
        }

        .main__craft-left-search-header {
            width: 100%;
            font-size: 1.8vh;
            font-weight: 500;
            color: #9575ac30;
            text-transform: uppercase;
            border-bottom: .2vh solid #38D6D4;
        }

        .main__craft-left-search-input {
            width: 100%;
            padding: .75vh;
            background: #9575ac301F;
            display: flex;
            gap: 1vh;
            margin-top: .5vh;
            display: flex;
            align-items: center;
        }

        .main__craft-left-search-input img {
            width: 1.5vh;
            height: 1.5vh;
            margin-left: .5vh;
        }

        .main__craft-left-search-input input {
            background: none;
            outline: none;
            border: none;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #28738A;
            text-transform: uppercase;
            font-weight: 400;
            width: 100%;
        }

        .main__craft-left-search-input input::placeholder {
            color: #28738A;
        }

        .main__craft-left-scroll-container {
            width: 100%;
            height: 72vh;
            margin-top: 2vh;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-content: flex-start;
            gap: 2vh;
            overflow-y: scroll;
        }

        .main__craft-left-scroll-container::-webkit-scrollbar {
            width: 0vh;
        }

        .main__craft-left-scroll-item {
            position: relative;
            width: 100%;
            height: 16vh;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            padding: 1vh;
        }

        .main__craft-left-scroll-item-header {
            width: 100%;
            padding: .3vh;
            background: linear-gradient(89.97deg, #9575ac30 0.02%, #9575ac30 24.49%, rgba(56, 215, 213, 0) 99.98%);
            font-size: 1.8vh;
            font-weight: 600;
            padding-left: 1.5vh;
            text-transform: uppercase;
            color: #135655;
        }

        .main__craft-left-scroll-item-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__craft-left-scroll-item-img img {
            width: 18vh;
            margin-top: 1vh;
        }

        .main__craft-left-scroll-item-materials {
            position: absolute;
            bottom: 1vh;
            left: 1vh;
            display: flex;
            gap: 1vh;
        }

        .main__craft-left-scroll-item-material-needs {
            padding-left: 1vh;
            padding-right: 1vh;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #9575ac30;
            font-weight: 600;
            background: #9575ac30;
        }

        .main__craft-right-preview-weapon-container {
            position: absolute;
            top: 50%;
            left: 60%;
            transform: translate(-50%, -50%);
            width: 116.6vh;
            height: 55.9vh;
        }

        .main__craft-right-preview-weapon-relative {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .main__craft-right-preview-weapon-bg-left {
            position: absolute;
            left: 0vh;
            top: 0vh;
        }

        .main__craft-right-preview-weapon-bg-left img,
        .main__craft-right-preview-weapon-bg-right img {
            width: 30vh;
        }

        .main__craft-right-preview-weapon-bg-right {
            position: absolute;
            right: 0;
            top: 0;
        }

        .main__craft-right-preview-weapon-bg-bottom {
            width: 100%;
            position: absolute;
            bottom: -23vh;
            left: 0vh;
        }

        .main__craft-right-preview-weapon-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__craft-right-preview-weapon-img img {
            width: 60vh;
            z-index: 10;
        }

        .main__craft-right-preview-weapon-bg-stripes {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
        }

        .main__craft-right-preview-weapon-bg-stripes img {
            width: 90vh;
        }

        .main__craft-right-preview-weapon-top-information {
            position: absolute;
            top: -1.35vh;
            left: 50%;
            transform: translateX(-50%);
            width: 51.6vh;
            text-transform: uppercase;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(89.97deg, rgba(95, 35, 138, 0.539) 0.02%, #9575ac30 50%, rgba(95, 35, 138, 0.539) 99.98%);
            font-size: 2.4vh;
            color: #175A59;
            font-weight: 600;
        }

        .main__craft-right-preview-weapon-bottom-information-container {
            position: absolute;
            bottom: -10vh;
            left: 50%;
            transform: translateX(-50%);
            width: 32.2vh;
        }

        .main__craft-right-preview-weapon-bottom-information-button {
            width: 100%;
            padding: 1vh;
            background: #9575ac30;
            display: flex;
            justify-content: center;
            align-items: center;
            text-transform: uppercase;
            color: #28738A;
            font-weight: 400;
            font-size: 1.4vh;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__craft-right-preview-weapon-bottom-information-button:hover {
            box-shadow: 0vh 0vh 1.7vh #9575ac30;
        }

        .main__craft-right-preview-weapon-bottom-information-content {
            margin-top: 2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .main__craft-right-preview-weapon-bottom-information-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #9575ac30;
            font-weight: 500;
        }

        .main__craft-right-preview-weapon-bottom-information-materials {
            font-size: 2.4vh;
            text-transform: uppercase;
            color: #D78438;
            text-shadow: 0vh 0vh 1.7vh #D78438;
            font-weight: 500;
        }

        .main__craft-right-preview-weapon-bottom-information-content-grid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2vh;
            margin-top: 3vh;
        }

        .main__craft-right-preview-weapon-bottom-information-content-item {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__craft-right-preview-weapon-bottom-information-checkbox {
            width: 2vh;
            height: 2vh;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .main__craft-right-preview-weapon-bottom-information-checkbox-fill {
            width: 1.3vh;
            height: 1.3vh;
            background: #9575ac30;
        }

        .main__craft-right-preview-weapon-bottom-information-add {
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #9575ac30;
            font-weight: 500;
        }

        .main__craft-right-bg-stripes {
            height: 100vh;
            position: absolute;
            right: 0;
            top: 0;
        }

        .main__craft-right-bg-stripes img {
            width: 12vh;
        }

        .main__craft-right-preview-weapon-color-selection {
            position: absolute;
            bottom: 1.8vh;
            right: 20.8vh;
            display: flex;
            gap: .5vh;
        }

        .main__craft-right-preview-weapon-color-item {
            width: 2.4vh;
            height: 3vh;
            transform: skew(-37deg);
            background: var(--clr-white);
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__craft-right-preview-weapon-color-item.active {
            opacity: .5;
        }

        .main__craft-right-preview-weapon-color-item:hover {
            opacity: .5;
        }

        .main__craft-right-preview-weapon-color-item.white {
            background: #cbc9c4 !important;
        }

        .main__craft-right-preview-weapon-color-item.purple {
            background:rgb(229, 73, 73) !important;
        }

        .main__craft-right-preview-weapon-color-item.gold {
            background: #FFBF44 !important;
        }

        .main__craft-bottom-information-content {
            position: absolute;
            bottom: 5vh;
            right: 5vh;
            width: 30vh;
        }

        .main__craft-bottom-information-content-grid {
            width: 100%;
            display: grid;
            grid-template-columns: .2fr 1fr;
            gap: 1vh;
        }

        .main__craft-bottom-information-content-icon {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__craft-bottom-information-content-icon img {
            width: 6vh;
        }

        .main__craft-bottom-information-content-item-header {
            font-size: 1.6vh;
            text-transform: uppercase;
            color: #D78438;
            text-shadow: 0vh 0vh 1.7vh #D78438;
            font-weight: 600;
        }

        .main__craft-bottom-information-content-item-text {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: #FFFFFF8C;
            font-weight: 500;
        }


        .main__craft-exit-menu {
            position: absolute;
            top: 5vh;
            right: 5vh;
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__craft-exit-text {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: #28738A;
            font-weight: 400;
            padding-left: 2vh;
            padding-right: 2vh;
            background: #9575ac301F;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 2vh;
        }

        .main__craft-exit-button {
            font-size: 1.4vh;
            text-transform: uppercase;
            font-weight: 500;
            color: #9575ac30;
            width: 3.8vh;
            height: 3.8vh;
            border: .4vh solid #9575ac30;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__char-top-selection {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            padding: 4vh;
        }

        .main__char-top-flexbox {
            width: 100%;
            height: 100%;
            overflow-y: scroll;
            display: flex;
            flex-direction: column;
            gap: 1vh;
        }

        .main__char-selection {
            position: absolute;
            top: 50%;
            right: -15vh;
            transform: translate(-50%, -50%) perspective(150vh) rotateY(-20deg);
            width: 35vh;
            perspective-origin: revert;
            height: 70vh;
            background: linear-gradient(260deg, rgba(138, 35, 35, 0.539) -1.73%, rgba(93, 15, 15, 0.493) 132.55%);
            border-image: linear-gradient(to right, #ac757530, rgba(138, 35, 35, 0.539)) 1;
        }

        .main__person-selection-scroll-item {
            width: 95%;
            padding: 2vh;
            padding-top: 2.5vh;
            padding-bottom: 2.5vh;
            background: rgba(255, 255, 255, .03);
            text-transform: uppercase;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__person-selection-scroll-item.active {
            border: .2vh solid white;
        }

        .main__person-selection-scroll-item:hover {
            color: rgb(255, 255, 255);
        }

        .main__char-top-item {
            width: 6.4vh;
            height: 5.2vh;
            background: linear-gradient(180deg, rgb(72, 48, 48) 0%, rgba(191, 58, 58, 0) 100%);
            border: .069vh solid;
            border-image-source: linear-gradient(180deg,rgb(62, 42, 42) 0%, rgb(91, 28, 28) 100%);
            border-image-slice: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            transform: skew(-12deg);
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__char-top-item:hover {
            background: linear-gradient(180deg, rgb(71, 54, 54) 0%, rgb(76, 24, 24) 100%);
        }

        .main__cloting-top-img img,
        .main__char-top-img img {
            width: 2vh;
            transform: skew(12deg);
            margin-top: .5vh;
        }

        .main__char-top-bg img {
            width: 81vh;
            margin-left: -.8vh;
        }

        .main__cloting-top-img.arrow img {
            width: 1vh;
        }

        .main__cloting-top-img.hat img {
            width: 3vh;
        }

        .main__cloting-top-img.glasses img,
        .main__cloting-top-img.ear img,
        .main__cloting-top-img.neck img,
        .main__cloting-top-img.shirt img,
        .main__cloting-top-img.gloves img,
        .main__cloting-top-img.vest img,
        .main__cloting-top-img.pants img {
            width: 3vh;
        } 

        .main__cloting-top-img.shoes img {
            width: 3.5vh;
        }

        .main__cloting-top-img.mask img {
            width: 2.5vh;
        }

        .main__char-top-item.active {
            background: linear-gradient(180deg, rgb(68, 46, 46) 0%, rgb(81, 29, 29) 100%);
        }

        .main__char-top-bg-stripes {
            position: absolute;
            top: -10vh;
            left: -2vh;
            z-index: -1;
        }

        .main__char-top-bg-stripes img {
            width: 32vh;
        }

        .main__char-bottom-container {
            bottom: 3vh;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 27.3vh;
        }

        .main__char-left-container {
            bottom: 3vh;
            position: absolute;
            height: 50%;
            left: 10vh;
            width: 27.3vh;
        }

        .main__char-right-container {
            bottom: 3vh;
            position: absolute;
            right: 10vh;
            width: 27.3vh;
        }

        .main__cloting-bottom-style,
        .main__char-bottom-style {
            width: 100%;
            margin-bottom: 1vh;
        }

        .main__cloting-bottom-style-header,
        .main__char-bottom-style-header {
            width: 100%;
            padding: 1vh;
            padding-top: .5vh;
            padding-bottom: .5vh;
            padding-left: 1.5vh;
            background: linear-gradient(89.97deg,rgb(62, 42, 42)rgb(99, 32, 32)#472063 2rgba(215, 56, 56, 0)215, 0) 99.98%);
            font-size: 1.8vh;
            text-transform: uppercase;
            font-weight: 600;
            color: #ffffff;
        }

        .main__char-bottom-flexbox {
            width: 100%;
            border: .09vh solid;
            border-image-source: linear-gradient(180deg,rgb(63, 43, 43) 0%, rgb(96, 36, 36) 100%);
            border-image-slice: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1vh;
        }

        .main__cloting-bottom-arrow,
        .main__char-bottom-arrow {
            width: 3.5vh;
            height: 3.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background:rgb(150, 38, 38);
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__cloting-bottom-arrow:hover,
        .main__char-bottom-arrow:hover {
            background:rgba(234, 117, 117, 0.87);
        }

        .main__cloting-bottom-arrow img,
        .main__char-bottom-arrow img {
            width: 1vh;
        }

        .main__cloting-bottom-count,
        .main__char-bottom-count {
            font-size: 2.1vh;
            text-transform: uppercase;
            color: #ffffff;
            text-shadow: 0vh 0vh 1.7vhrgb(143, 51, 51);
        }

        .main__cloting-bottom-count input,
        .main__char-bottom-count input {
            font-size: 2.1vh;
            text-transform: uppercase;
            color: #ffffff;
            text-shadow: 0vh 0vh 1.7vhrgb(121, 47, 47);
            width: 100%;
            text-align: center;
            outline: none;
            border: none;
            background: none;
            font-family: var(--ff-special);
            font-weight: 600;
        }

        .main__cloting-bottom-count input::placeholder,
        .main__char-bottom-count input::placeholder {
            color: #ffffff;
            text-shadow: 0vh 0vh 1.7vhrgb(134, 45, 45);
            font-weight: 600;
        }

        .main__cloting-bottom-button,
        .main__char-bottom-button {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background:rgb(136, 57, 57);
            cursor: pointer;
            transition: .2s ease-in;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #ffffff;
            font-weight: 400;
            padding-top: .25vh;
            padding-bottom: .25vh;
            margin-top: 1vh;
        }

        .main__cloting-bottom-button:hover,
        .main__char-bottom-button:hover {
            box-shadow: 0vh 0vh 1.7vhrgb(212, 120, 120);
        }

        .main__char-bottom-slider,
        .main__identity-item-slider-container {
            width: 100%;
            background: linear-gradient(180deg, rgb(86, 41, 41) 0%, rgb(116, 37, 37) 100%);
            border: .069vh solid;
            border-image-source: linear-gradient(180deg,rgb(88, 48, 48) 0%, rgb(132, 38, 38) 100%);
            border-image-slice: 1;
            margin-bottom: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .5vh;
        }

        .main__identity-item-slider-container {
            margin-top: .25vh;
            margin-bottom: -.25vh;
        }

        .main__char-bottom-slider input,
        .main__identity-item-slider-container input {
            -webkit-appearance: none;
            width: 100%;
            height: .5vh;
            background: #9575ac301F;
        }

        .main__char-bottom-slider input::-webkit-slider-thumb,
        .main__identity-item-slider-container input::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 1vh;
            height: 1vh;
            background: #ffffff;
            cursor: pointer;
        }

        .main__fuelinfo-bottom-container {
            width: 100%;
            padding: 0.5vh;
            display: flex;
            justify-content: space-around;
            align-items: center;
            gap: 1vh;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: var(--color-white);
            background: radial-gradient(662.13% 347.88% at 50.17% 35.63%, rgba(255, 69, 69, 0.25) 0%, rgba(255, 69, 69, 0) 100%);
            border: 0.1vh solid #ff4545;
            box-shadow: 0 0 3.7vh #ff4545 inset, 0 0.4vh 5.6vh #ff454540;
            border-radius: 1vh 1vh 0vh 0vh;
        }

        .main__fuelinfo-bottom-flexbox {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5vh;
        }

        .main__fuelinfo-bottom-flexbox-item {
            width: 15.3vh;
            height: 21.6vh;
            padding: .3vh;
        }

        .main__fuelinfo-top-color {
            width: 100%;
            height: .75vh;
            margin-top: .15vh;
        }

        .main__fuelinfo-top-color.left {
            background: #9575ac30;
        }

        .main__fuelinfo-top-color.middle {
            background: #6BD738;
        }

        .main__fuelinfo-top-color.right {
            background: #FF4949;
        }

        .main__fuelinfo-icon-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 3vh;
        }

        .main__fuelinfo-icon-container img {
            width: 3vh;
        }

        .main__fuelinfo-icon-container.bill img {
            width: 2vh;
        }

        .main__fuelinfo-information-container {
            margin-top: 1.5vh;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .main__fuelinfo-information-header {
            font-size: 1.4vh;
            text-transform: uppercase;
            color: var(--clr-white);
            font-weight: 600;
        }

        .main__fuelinfo-information-text {
            margin-top: -.25vh;
            font-size: 2.4vh;
        }

        .main__fuelinfo-bottom-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            margin-bottom: 1vh;
        }

        .main__fuelinfo-bottom-small-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #FFFFFF75;
            font-weight: 500;
        }

        .main__fuelinfo-bottom-big-header {
            font-size: 2.4vh;
            text-transform: uppercase;
            color: var(--clr-white);
            font-weight: 700;
            margin-top: -.25vh;
        }

        .main__fuel-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .main__fuel-container-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 58vh;
        }

        .main__fuel-header-left-stripe {
            width: 0.1vh;
            height: 3.5vh;
            background: rgba(255, 255, 255, 0.5);
            margin-right: 0.5vh;
        }

        .main__fuel-header-left {
            font-family: var(--ff-arame);
            font-size: 1.5vh;
        }

        .main__fuel-header-left p:first-child {
            font-weight: 100;
            color: #ffffff80;
            letter-spacing: 0.5vh;
            line-height: 1.5vh;
        }

        .main__fuel-header-left P:last-child {
            font-weight: 700;
            font-size: 2.6vh;
            color: #fff;
            line-height: 2.6vh;
        }

        .main__fuel-header-container-right {
        display: flex;
        align-items: center;
        gap: 1vh;
        }
        
        .main__fuel-header-container-right-close-container {
            width: 3.5vh;
            height: 3.5vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(184, 28, 37, 0) 100%);
            box-shadow: 0 0 3.7vh #ffffff26 inset;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            margin-left: 12vh;
        }

        .main__fuel-header-container-right-close-container i {
            transition: 0.2s ease-in;
        }

        .main__fuel-header-container-right-close-container:hover {
            box-shadow: 0 0 1.7vh #ffffff80 inset;
        }

        .main__fuel-header-container-right-close-container:hover i {
            transform: rotateY(180deg);
        }

        .main__fuel-header-bg img {
            width: 6vh;
        }

        .main__fuel-fuel-content-container {
            position: relative;
            width: 100%;
            height: 8vh;
            box-shadow: 0 0 10vh #ffffff26 inset;
            margin-top: 0.5vh;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            overflow: hidden;
        }

        .main__fuel-fuel-content-img {
            position: absolute;
            top: 0vh;
            left: 0;
            width: 100%;
            height: 130%;
            overflow: hidden;
        }

        .main__fuel-fuel-content-img div {
            height: 2.5vh;
            width: 2.5vh;
            border-radius: 5vh;
            position: absolute;
            top: 10%;
            left: 10%;
            animation: 4s linear infinite;
        }

        .main__fuel-fuel-content-img div:nth-child(1) {
            top: 20%;
            left: 20%;
            animation: animate 8s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(2) {
            top: 60%;
            left: 80%;
            animation: animate 10s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(3) {
            top: 40%;
            left: 40%;
            animation: animate 3s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(4) {
            top: 66%;
            left: 30%;
            animation: animate 7s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(5) {
            top: 90%;
            left: 10%;
            animation: animate 9s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(6) {
            top: 30%;
            left: 60%;
            animation: animate 5s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(7) {
            top: 70%;
            left: 20%;
            animation: animate 8s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(8) {
            top: 75%;
            left: 60%;
            animation: animate 10s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(9) {
            top: 50%;
            left: 50%;
            animation: animate 6s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(10) {
            top: 45%;
            left: 20%;
            animation: animate 10s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(11) {
            top: 10%;
            left: 90%;
            animation: animate 9s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(12) {
            top: 20%;
            left: 70%;
            animation: animate 7s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(13) {
            top: 20%;
            left: 20%;
            animation: animate 8s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        .main__fuel-fuel-content-img div:nth-child(14) {
            top: 60%;
            left: 5%;
            animation: animate 6s linear infinite;
            border: 0.2vh solid #17c164;
            box-shadow: 0 0 1.7vh #17c164, 0 0 1vh #17c164 inset;
        }

        .main__fuel-fuel-content-img div:nth-child(15) {
            top: 90%;
            left: 80%;
            animation: animate 9s linear infinite;
            border: 0.2vh solid #0274ed;
            box-shadow: 0 0 1.7vh #0274ed, 0 0 1vh #0274ed inset;
        }

        @keyframes animate {
            0% {
                transform: scale(0) translateY(0) rotate(70deg);
            }
            to {
                transform: scale(1.3) translateY(-10vh) rotate(360deg);
            }
        }

        .main__fuel-fuel-content-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50%;
            height: 100;
            background: url(../img/wave.png) repeat-x;
        }

        .main__fuel-fuel-content-wave-sphere {
            position: absolute;
            left: 50%;
            transform: translate(-50%);
            bottom: -12vh;
            width: 15vh;
            height: 15vh;
            background: var(--clr-pink);
            border-radius: 50%;
            filter: blur(5vh);
        }

        .main__fuel-fuel-information-container {
            margin-top: 1vh;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1vh;
        }

        .main__fuel-fuel-information-left-icon {
            width: 4vh;
            height: 4vh;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 3vh #ffffff26 inset;
        }

        .main__fuel-fuel-information-left-container,
        .main__fuel-fuel-information-right-container {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__fuel-fuel-information-left-icon img {
            width: 100%;
        }

        .main__fuel-fuel-information-left-information p:last-child {
            font-family: Rajdhani;
            font-size: 1.5vh;
            color: #fff;
        }

        .main__fuel-fuel-information-left-information p:first-child {
            font-family: Rajdhani;
            font-size: 1.2vh;
            color: #ffffff80;
        }

        .main__fuel-fuel-item-information-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1vh;
            margin-top: 2vh;
        }

        .main__fuel-fuel-item-information-item {
            width: 100%;
            height: 10vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 3vh #ffffff26 inset;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            border-radius: 0.5vh;
        }

        .main__fuel-fuel-item-information-item-icon {
            width: 8vh;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 3vh;
            color: #0274ed;
            text-shadow: 0vh 0vh 1.7vh #0274ed;
        }

        .main__fuel-fuel-item-information-item-icon.green {
            color: #44ff6d;
            text-shadow: 0vh 0vh 1.7vh #44ff6d;
        }

        .main__fuel-fuel-item-information-item-icon.pink {
            color: #bd1b74;
            text-shadow: 0vh 0vh 1.7vh #bd1b74;
        }

        .main__fuel-fuel-item-information-item-text {
            color: #fff;
            font-size: 1.5vh;
        }

        .main__fuel-fuel-item-information-item-text p:first-child {
            font-size: 1.2vh;
            color: #ffffff80;
            margin-top: 0.5vh;
        }

        .main__fuel-fuel-bottom-price-grid-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.5vh;
            margin-top: 2vh;
        }

        .main__fuel-fuel-bottom-price-grid-item {
            width: 100%;
            height: 8.5vh;
            background: rgba(255, 255, 255, 0.03);
            border: 0.1vh solid rgba(255, 255, 255, 0.03);
            border-radius: 0.4vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.4vh;
            color: #44ff6d;
            text-shadow: 0vh 0vh 1.7vh #44ff6d;
        }

        .main__fuel-fuel-bottom-btn-grid {
            margin-top: 0.5vh;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5vh;
        }
        .main__fuel-fuel-bottom-btn-grid2 {
            margin-top: 0.5vh;
            width: 100%;
            display: flex;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5vh;
        }

        .main__fuel-fuel-bottom-btn {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5vh;
            padding: 1vh;
            border-radius: 0.4vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            font-size: 1.2vh;
            color: var(--color-white);
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fuel-fuel-bottom-btn i {
            font-size: 1.5vh;
        }

        .main__fuel-fuel-bottom-btn:hover {
            box-shadow: 0 0 3vh #03381b inset, 0 0 1.7vh #17c164;
            background: #149c51;
            border: 0.1vh solid transparent;
        }

        .main__gunshop-top-selection {
            position: absolute;
            width: 95vw;
            display: flex;
            justify-content: center;
            align-items: center; 
            border-bottom: .2vh solid #9575ac3040;
            top: 7.5vh;
            margin-left: 11vh;
        }

        .main__gunshop-top-selection-item-bar {
            width: 110vh;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5vh;
            margin-bottom: 2vh;
            height: 5.2vh;
        }

        .main__gunshop-top-selection-item-arrow {
            width: 5.2vh;
            height: 5.2vh;
            border: .069vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            transform: skew(-10deg);
        }

        .main__gunshop-top-selection-item-arrow img {
            width: 2vh;
            transform: skew(10deg);
        }

        .main__gunshop-top-selection-item {
            width: 21vh;
            transform: skew(-10deg);
        }

        .main__gunshop-top-selection-item-grid {
            width: 100%;
            display: grid;
            grid-template-columns: .35fr 1fr;
            gap: .5vh;
        }

        .main__gunshop-top-selection-item-icon {
            width: 100%;
            height: 5.2vh;
            border: .069vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__gunshop-top-selection-item-icon img {
            transform: skew(10deg);
            width: 3vh;
        }

        .main__gunshop-top-selection-item-icon.pistol img {
            width: 2.5vh;
        }

        .main__gunshop-top-selection-item-name {
            width: 100%;
            height: 5.2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border: .069vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
        }

        .main__gunshop-top-selection-item-name p {
            transform: skew(10deg);
            font-size: 1.4vh;
            text-transform: uppercase;
            font-weight: 500;
            color: var(--clr-white);
        }

        .main__gunshop-left-scroll-container {
            position: absolute;
            width: 37.3vh;
            height: 75vh;
            top: 20vh;
            left: 5vh;
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            justify-content: flex-start;
            align-content: flex-start;
            overflow-y: scroll;
            gap: 2vh;
        }

        .main__gunshop-left-scroll-container::-webkit-scrollbar {
            width: 0vh;
        }

        .main__gunshop-left-scroll-item {
            position: relative;
            width: 34.5vh;
            height: 13.3vh;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            border-top: .5vh solid #9575ac30;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            transform: skew(-10deg);
            margin-left: 1.25vh;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            overflow: hidden;
            padding: 1vh;
        }

        .main__gunshop-left-scroll-sphere {
            position: absolute;
            bottom: -5vh;
            left: 50%;
            transform: translateX(-50%);
            width: 10vh;
            height: 10vh;
            background: #9575ac30;
            border-radius: 50%;
            filter: blur(7vh);
            z-index: -1;
        }

        .main__gunshop-left-scroll-item-header {
            transform: skew(10deg);
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #d8bfe9b0;
            font-weight: 600;
            margin-top: -.5vh;
        }

        .main__gunshop-left-scroll-item-price {
            position: absolute;
            bottom: .5vh;
            right: 1vh;
            transform: skew(10deg);
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #eae0f0e8;
        }

        .main__gunshop-left-scroll-item-amount {
            position: absolute;
            bottom: .5vh;
            left: 1vh;
            transform: skew(10deg);
            font-size: 1.8vh;
            text-transform: uppercase;
            color: rgba(255, 255, 255, .5);
            font-weight: 500;
        }

        .main__gunshop-left-scroll-weapon-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__gunshop-left-scroll-weapon-img-icons {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__gunshop-left-scroll-weapon-img-icons img {
            transform: skew(10deg);
            max-width: 10vh;
            margin-top: 1vh;
        }

        .main__gunshop-left-scroll-weapon-img img {
            transform: skew(10deg);
            width: 20vh;
            margin-top: 1vh;
        }

        .main__gunshop-left-line {
            position: absolute;
            left: 45vh;
            width: .2vh;
            height: 75vh;
            background: #9575ac3040;
            bottom: 5vh;
        }

        .main__gunshop-left-weapon-big-name {
            position: absolute;
            left: 50vh;
            top: 19vh;
        }

        .main__gunshop-left-weapon-big-name-header {
            font-size: 3.6vh;
            text-transform: uppercase;
            color: #9575ac30;
            font-weight: 600;
        }

        .main__gunshop-left-weapon-big-information {
            display: flex;
            gap: 1vh;
        }

        .main__gunshop-left-weapon-big-item {
            padding: .25vh;
            padding-left: 1vh;
            padding-right: 1vh;
            background: #9575ac30;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #9575ac30;
        }

        .main__gunshop-cirle-container {
            position: absolute;
            left: 60vh;
            top: 25vh;
        }

        .main__gunshop-cirle-container img {
            width: 50vh;
        }

        .main__gunshop-circle-big-weapon {
            position: absolute;
            left: 84vh;
            top: 30vh;
        }

        .main__gunshop-circle-big-weapon img {
            width: 50vh;
        }

        .main__gunshop-bottom-line {
            width: 145vh;
            height: .2vh;
            position: absolute;
            left: 50vh;
            bottom: 25vh;
            background: #9575ac3040;
        }

        .main__gunshop-statistics-container {
            position: absolute;
            left: 50vh;
            bottom: 10vh;
        }

        .main__gunshop-flex-container {
            display: flex;
            gap: 2vh;
        }

        .main__gunshop-statistics-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #9575ac30;
            font-weight: 500;
            margin-bottom: 1vh;
        }

        .main__gunshop-item-line img {
            height: 10vh;
        }

        .main__gunshop-item-number-big {
            font-size: 4vh;
            text-transform: uppercase;
            text-align: center;
            font-weight: 400;
        }

        .main__gunshop-item-number-text {
            font-size: 2.4vh;
            text-transform: uppercase;
            font-weight: 500;
            margin-top: -.5vh;
        }

        .main__gunshop-item-number-big.blue,
        .main__gunshop-item-number-text.blue {
            color: #d4c2e09a;
            text-shadow: 0vh 0vh 1.7vh #dad1e1df;
        }

        .main__gunshop-item-number-big.orange,
        .main__gunshop-item-number-text.orange {
            color: #D78438;
            text-shadow: 0vh 0vh 1.7vh #D78438;
        }

        .main__gunshop-item-number-big.red,
        .main__gunshop-item-number-text.red {
            color: #FF4646;
            text-shadow: 0vh 0vh 1.7vh #FF4646;
        }

        .main__gunshop-item-number-big.green,
        .main__gunshop-item-number-text.green {
            color: #d4c2e09a;
            text-shadow: 0vh 0vh 1.7vh #dad1e1df;
        }

        .main__gunshop-price-container {
            position: absolute;
            left: 129vh;
            bottom: 7vh;
        }

        .main__gunshop-price-header {
            font-size: 1.8vh;
            color: #9575ac30;
            font-weight: 500;
        }

        .main__gunshop-price-text {
            font-size: 7vh;
            text-transform: uppercase;
            color: #e5dceb9d;
            text-shadow: 0vh 0vh 1.7vh #e6e1e9dd;
            font-weight: 400;
            margin-top: -1vh;
        }

        .main__gunshop-buy-button {
            width: 26.5vh;
            padding-top: .5vh;
            padding-bottom: .5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #5b426dac;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #d6b1e8;
            font-weight: 400;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__gunshop-buy-button:hover {
            box-shadow: 0vh 0vh 1.7vh #73538b30;
        }

        .main__gunshop-bottom-slider {
            width: 100%;
            background: linear-gradient(180deg, rgba(95, 35, 138, 0.539) 0%, rgba(95, 35, 138, 0.539) 100%);
            border: .069vh solid;
            border-image-source: linear-gradient(180deg, #9575ac30 0%, rgba(95, 35, 138, 0.539) 100%);
            border-image-slice: 1;
            margin-bottom: 1vh;
            margin-top: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .5vh;
        }

        .main__gunshop-bottom-slider input {
            -webkit-appearance: none;
            width: 100%;
            height: .5vh;
            background: #9575ac301F;
        }

        .main__gunshop-bottom-slider input::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 1vh;
            height: 1vh;
            background: #38D6D4;
            cursor: pointer;
        }

        .main__gunshop-bottom-slider-text {
            font-size: 1.3vh;
            text-transform: uppercase;
            color: #9575ac30;
            text-shadow: 0vh 0vh 1.7vh #9575ac30;
            font-weight: 400;
            margin-top: -0.65vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__cardealer-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120vh;
            padding: 4vh;
            border-radius: 1vh;
            background: url(img/vehicleshop/bg.png);
            background-size: cover;
            background-position: center;
        }
        
        .main__cardealer-grid-container {
            width: 100%;
            display: grid;
            grid-template-columns: .4fr 1fr;
            gap: 1vh;
            margin-top: 2vh;
        }
        
        .main__cardealer-grid-left-container {
            position: relative;
            width: 100%;
            padding: 2vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 2.5vh rgba(255, 255, 255, .15) inset;
            background-size: cover;
            background-position: center top;
        }
        
        .main__cardealer-grid-left-header {
            position: absolute;
            top: 0vh;
            left: 0vh;
            padding: 1.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            background: rgba(255, 255, 255, .15);
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
        }
        
        .main__cardealer-grid-left-header-icon {
            position: absolute;
            top: .75vh;
            right: .75vh;
            width: 3vh;
            height: 3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2vh;
            color: var(--color-white);
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__cardealer-grid-left-header-icon:hover {
            box-shadow: 0vh 0vh 3vh #382403 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__cardealer-grid-left-car-img-container {
            margin-top: 3vh;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 15vh;
        }
        
        .main__cardealer-grid-left-car-img-container img {
            width: 100%;
        }
        
        .main__cardealer-grid-left-car-header {
            text-align: center;
            font-size: 1.4vh;
            color: var(--color-white);
            margin-top: 2vh;
        }
        
        .main__corleone-weapon-grid-right-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5vh;
            margin-top: 2vh;
        }
        
        .main__corleone-weapon-grid-right-information-item {
            display: flex;
            align-items: center;
            gap: 2vh;
            width: 100%;
            color: var(--color-white);
        }
        
        .main__corleone-weapon-grid-right-information-item-icon {
            width: 6vh;
            height: 7vh;
            background: url(img/vehicleshop/icon.svg);
            background-size: cover;
        }
        
        .main__corleone-weapon-grid-right-information-item-information p {
            font-size: 1.2vh;
            color: rgba(255, 255, 255, .5);
        }
        
        .main__corleone-weapon-grid-right-information-item-information p:nth-last-child(1) {
            font-size: 1.6vh;
            color: var(--color-white);
        }
        
        .main__corleone-grid-right-information-item-flex-container {
            display: flex;
            gap: .75vh;
            font-size: 2vh;
            color: rgba(255, 255, 255, 0.15);
            font-family: var(--ff-druk);
        }
        
        .main__corleone-grid-right-information-item-flex-container p.active {
            color: #c11717;
            text-shadow: 0vh 0vh 1.7vh #c11717;
        }
        
        .main__cardealer-grid-left-btn-test-drive {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .5vh;
            text-align: center;
            font-size: 1.2vh;
            color: var(--color-white);
            cursor: pointer;
            transition: .2s ease-in;
            border-radius: .2vh;
            border: .1vh solid #ff4545;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(255, 69, 69, 0.25) 0%, rgba(255, 69, 69, 0) 100%);
            box-shadow: 0 0 3.7vh #ff4545 inset, 0 0.4vh 5.6vh #ff454540;
            margin-top: 1vh;
        }
        
        .main__cardealer-grid-left-btn-test-drive:hover {
            box-shadow: 0vh 0vh 1.7vh #ff4545 inset;
        }
        
        .main__cardealer-grid-left-btn-grid-container {
            margin-top: 1vh;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
        }
        
        .main__cardealer-grid-left-btn-grid-buy {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1vh;
            text-align: center;
            font-size: 1.2vh;
            color: var(--color-white);
            cursor: pointer;
            transition: .2s ease-in;
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
        }
        
        .main__cardealer-grid-left-btn-grid-buy:hover {
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__cardealer-grid-left-btn-grid-price {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1vh;
            text-align: center;
            font-size: 1.2vh;
            border-radius: .2vh;
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
            color: #fff;
        }
        
        .main__cardealer-grid-right-container {
            width: 100%;
            height: 69.1vh;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1vh;
            align-content: flex-start;
            overflow-y: scroll;
            overflow-x: hidden;
            padding-right: 1vh;
        }
        
        .main__cardealer-grid-right-container::-webkit-scrollbar {
            width: .3vh;
        }
        
        .main__cardealer-grid-right-container::-webkit-scrollbar-track {
            width: .3vh;
            background: rgba(255, 255, 255, .05);
        }
        
        .main__cardealer-grid-right-container::-webkit-scrollbar-thumb {
            width: .3vh;
            background: #c11717;
            box-shadow: 0vh 0vh 1.7vh #c11717;
        }
        
        .main__cardealer-grid-right-scroll-item {
            position: relative;
            width: 100%;
            padding: 1vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 2.7vh rgba(255, 255, 255, .15) inset;
            background-size: cover;
            background-position: center top;
            transition: .2s ease-in;
        }
        
        .main__cardealer-grid-right-scroll-item-header {
            display: flex;
            width: 100%;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .main__cardealer-grid-right-scroll-item-header-left p:first-child {
            color: #c11717;
            text-shadow: 0vh 0vh 1.7vh #c11717;
            font-size: 1.4vh;
            font-weight: 600;
        }
        
        .main__cardealer-grid-right-scroll-item-header-left p:last-child {
            font-size: 1.2vh;
            color: rgba(255, 255, 255, .5);
        }

        .main__cardealer-grid-right-scroll-item-header-right {
            font-size: 1.2vh;
            color: var(--color-white);
            padding: .5vh;
            padding-left: 1vh;
            padding-right: 1vh;
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            text-transform: uppercase;
        }
        
        .main__cardealer-img-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
        }
        
        .main__cardealer-img-container img {
            width: 100%;
            height: 100%;
        }
        
        .main__cardealer-img-btn {
            margin-top: 13vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1vh;
            text-align: center;
            font-size: 1.2vh;
            color: var(--color-white);
            cursor: pointer;
            transition: .2s ease-in;
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 2vh rgba(255, 255, 255, .15) inset;
            text-transform: uppercase;
        }
        
        .main__cardealer-img-btn:hover,
        .main__cardealer-img-btn.active {
            box-shadow: 0 0 3vh #380303 inset, 0 0 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__cardealer-category-header {
            position: absolute;
            top: 4.25vh;
            left: 37vh;
            width: 73vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main__cardealer-category-header-arrow-btn {
            width: 3.5vh;
            height: 3.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2vh;
            color: var(--color-white);
            cursor: pointer;
            transition: .2s ease-in;
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
        }
        
        .main__cardealer-category-header-arrow-btn.disabled {
            cursor: not-allowed;
            opacity: .5;
        }
        
        .main__cardealer-category-header-arrow-btn:not(.disabled):hover {
            box-shadow: 0 0 3vh #380303 inset, 0 0 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__cardealer-category-header-category-item-container {
            display: flex;
            align-content: flex-start;
            flex-direction: row;
            gap: 1vh;
            width: 63vh;
            overflow: hidden;
        }
        
        .main__cardealer-category-header-category-item {
            height: 3.5vh;
            flex-shrink: 0;
            width: 15vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            background-size: 100%;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--color-white);
            border-radius: .2vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__cardealer-category-header-category-item:hover,
        .main__cardealer-category-header-category-item.active {
            box-shadow: 0 0 3vh #380303 inset, 0 0 1.7vh #c11717;
            background: #9c1414;
            border: 0.1vh solid transparent;
        }
        
        .main__cardealer-grid-right-scroll-item.active {
            border: .1vh solid rgba(255, 255, 255, .05);
            box-shadow: 0vh 0vh 2.5vh rgba(0, 0, 0, 1) inset;
        }
        
        .mt-1 img {
            width: 7vh;
            margin-top: .5vh;
        }
        
        .main__cardealer-bottom-container {
            position: absolute;
            bottom: 0vh;
            left: 50%;
            transform: translate(-50%, -20%);
            width: 30vh;
            padding: 2vh;
            border-radius: 1vh;
            background: url(img/vehicleshop/bg.png);
            background-size: cover;
            background-position: center;
        }

        .main__driving-big-grid-container {
            width: 100%;
            display: grid;
            grid-template-columns: 1fr 0.5fr;
            gap: 2vh;
            margin-top: 2vh;
        }

        .main__driving-big-botton-grid-container {
            margin-top: 2vh;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2vh;
        }

        .main__driving-big-grid-left {
            width: 100%;
        }

        .main__driving-big-grid-right {
            width: 100%;
        }

        .main__driving-big-item-left {
            position: relative;
            overflow: hidden;
            width: 100%;
            border-radius: var(--border-radius-close);
            background: radial-gradient(83.27% 113.89% at 49.76% 0%, rgba(57, 113, 217, 0.15) 0%, rgba(57, 113, 217, 0) 100%);
            padding: 3.6vh;
        }

        .main__driving-big-item-bottom {
            position: relative;
            width: 100%;
            overflow: hidden;
            background: radial-gradient(83.27% 113.89% at 49.76% 0%, rgba(57, 113, 217, 0.15) 0%, rgba(57, 113, 217, 0) 100%);
            border-radius: var(--border-radius-close);
            height: 30vh;
            padding-bottom: 1.5vh;
        }

        .main__driving-big-item-right {
            width: 100%;
            position: relative;
            overflow: hidden;
            height: 100%;
            border-radius: var(--border-radius-close);
            background: radial-gradient(83.27% 113.89% at 49.76% 0%, rgba(57, 113, 217, 0.15) 0%, rgba(57, 113, 217, 0) 100%);
            padding-bottom: 5vh;
            padding-left: 1vh;
            padding-right: 1vh;
        }

        .main__driving-big-category {
            font-size: 2.5vh;
            text-transform: uppercase;
            color: #9575ac30;
            font-weight: 600;
        }

        .main__driving-big-header {
            font-size: 3.6vh;
            text-transform: uppercase;
            font-weight: 900;
        }

        .main__driving-big-header-bottom {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 100%;
            justify-content: flex-end;
            margin-top: -4vh;
        }

        .main__driving-big-type {
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, #3971d9 0%, #000 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .main__driving-big-type-bottom,
        .main__driving-big-type-right {
            font-size: 3.6vh;
            text-transform: uppercase;
            font-weight: 900;
            text-align: center;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, #3971d9 0%, #000 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .main__driving-big-description {
            width: 27vh;
            font-size: 1.2vh;
            color: var(--color-opacity-white);
            z-index: 100;
        }

        .main__driving-big-description-bottom,
        .main__driving-big-description-right {
            font-size: 1.25vh;
            color: var(--color-opacity-white);
            text-align: center;
        }

        .main__driving-big-bg-container {
            position: absolute;
            top: -22vh;
            right: -15vh;
            z-index: -1;
            animation: stripesanimation 3s infinite linear;
        }

        .main__driving-big-bg-container img {
            width: 60vh;
        }

        @keyframes stripesanimation {
            0% {
            margin-top: 0vh;
            }
            50% {
            margin-top: 2vh;
            }
            to {
            margin-top: 0vh;
            }
        }

        .main__driving-big-img-container {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0vh;
        }

        .main__driving-big-img-container img {
            width: 30vh;
        }

        .main__driving-big-bg-container-bottom {
            position: absolute;
            left: 50%;
            transform: translate(-50%);
            top: -18vh;
            animation: stripesanimation 3s infinite linear;
            z-index: -1;
        }

        .main__driving-big-bg-container-bottom img {
            width: 40vh;
        }

        .main__driving-big-img-container-bottom {
            position: absolute;
            right: 0vh;
            top: 1vh;
        }

        .main__driving-big-img-container-bottom img {
            width: 18vh;
        }

        .main__driving-big-img-container-bottom.truck img {
            width: 16.5vh;
        }

        .main__driving-big-bg-container-right {
            position: absolute;
            top: -22vh;
            left: 50%;
            transform: translate(-50%);
            animation: stripesanimation 3s infinite linear;
        }

        .main__driving-big-bg-container-right img {
            width: 60vh;
        }

        .main__driving-big-img-container-right {
            position: absolute;
            top: 5vh;
            right: 0vh;
            width: 100%;
        }

        .main__driving-big-img-container-right img {
            width: 100%;
        }

        .main__driving-grid-right-big-item-text-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: flex-end;
        }

        .main__driving-big-img-sphere-red {
            position: absolute;
            top: 50%;
            left: -15.5vh;
            transform: translateY(-50%);
            margin-top: 5vh;
            width: 30vh;
            height: 30vh;
            border-radius: 50%;
            border: 1.5vh solid #FF4646;
            box-shadow: 0vh 0vh 1.7vh #FF4646;
            z-index: -1;
        }

        .main__driving-big-img-sphere-white {
            position: absolute;
            top: 50%;
            left: -15.5vh;
            transform: translateY(-50%);
            margin-top: 5vh;
            width: 30vh;
            height: 30vh;
            border-radius: 50%;
            border: 1.5vh solid var(--clr-white);
            box-shadow: 0vh 0vh 1.7vh var(--clr-white);
            z-index: -1;
        }

        .main__driving-big-img-sphere-blue {
            position: absolute;
            top: 50%;
            left: -15.5vh;
            transform: translateY(-50%);
            margin-top: 5vh;
            width: 30vh;
            height: 30vh;
            border-radius: 50%;
            border: 1.5vh solid #469BFF;
            box-shadow: 0vh 0vh 1.7vh #469BFF;
            z-index: -1;
        }

        .main__driving-big-img-sphere-purple {
            position: absolute;
            top: 50%;
            left: -15.5vh;
            transform: translateY(-50%);
            margin-top: 5vh;
            width: 30vh;
            height: 30vh;
            border-radius: 50%;
            border: 1.5vh solid #8146FF;
            box-shadow: 0vh 0vh 1.7vh #8146FF;
            z-index: -1;
        }

        .main__driving-big-img-sphere-pink {
            position: absolute;
            top: 50%;
            left: -15.5vh;
            transform: translateY(-50%);
            margin-top: 5vh;
            width: 30vh;
            height: 30vh;
            border-radius: 50%;
            border: 1.5vh solid #E546FF;
            box-shadow: 0vh 0vh 1.7vh #E546FF;
            z-index: -1;
        }

        .main__driving-big-button {
            position: relative;
            width: 17vh;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0) 100%);
            box-shadow:
            0 0 3.7vh #3971d9 inset,
            0 0.4vh 5.6vh #3971d940;
            border: 0.15vh solid #3971d9;
            border-radius: 0.5vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.2vh;
            color: var(--color-white);
            text-transform: uppercase;
            margin-top: 2vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            padding: 0.5vh 0.5vh 0.5vh 1vh;
        }

        .main__driving-big-button-price {
            font-size: 1.2vh;
            color: var(--color-white);
            display: flex;
            justify-content: flex-end;
            align-items: center;
            border: 0.1vh solid #45ff41;
            background: linear-gradient(0deg, rgba(69, 255, 65, 0.25) 0%, rgba(69, 255, 65, 0) 100%);
            box-shadow:
            0 0 3.7vh #45ff41 inset,
            0 0.4vh 5.6vh #45ff4140;
            padding: 0.5vh 1vh;
            border-radius: 0.2vh;
            text-align: right;
            transition: 0.2s ease-in;
            border-radius: 0.25vh;
        }

        .main__driving-big-button p {
            z-index: 100;
        }

        .main__driving-big-button:not(.active):hover {
            background: linear-gradient(0deg, rgba(69, 255, 65, 0.25) 0%, rgba(69, 255, 65, 0) 100%);
            box-shadow:
            0 0 3.7vh #45ff41 inset,
            0 0.4vh 5.6vh #45ff4140;
            border: 0.15vh solid #45ff41;
        }

        .main__driving-big-button:not(.active):hover
        .main__driving-big-button-price {
            background: rgba(0, 0, 0, 0.25);
            border: 0.1vh solid rgba(0, 0, 0, 0.25);
            box-shadow: none;
        }

        .main__driving-big-button {
            cursor: not-allowed;
        }

        .main__driving-big-button.active:hover {
            background: linear-gradient(0deg, rgba(69, 255, 65, 0.25) 0%, rgba(69, 255, 65, 0) 100%);
            box-shadow:
            0 0 3.7vh #45ff41 inset,
            0 0.4vh 5.6vh #45ff4140;
            border: 0.15vh solid #45ff41;
            cursor: pointer;
        }

        .main__driving-helicopter {
            top: 26.5vh !important;
        }

        .main__driving-boat {
            top: 26.5vh !important; 
        }

        .main__driving-question-container {
            position: absolute;
            top: 54.5%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 94vh;
        }

        .main__driving-question-container-header {
            position: relative;
            overflow: hidden;
            width: 100%;
            padding: 2vh;
            border-radius: 0.4vh;
            background: radial-gradient(83.27% 113.89% at 49.76% 0%, rgba(57, 113, 217, 0.15) 0%, rgba(57, 113, 217, 0) 100%);
            margin-top: 2vh;
        }

        .main__driving-question-header {
            font-family: var(--ff-druk);
            font-size: 3.7vh;
            font-weight: 700;
            background: radial-gradient(539.11% 2290.5% at 50.17% 50%, #3971d9 10%, #00000075 10%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-transform: uppercase;
        }

        .main__driving-question-desc {
            color: #ffffff80;
            width: 55vh;
            font-size: 1.4vh;
            font-weight: 400;
        }

        .main__driving-question-container-question {
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #ffffff30;
            padding-left: 2vh;
            padding-right: 2vh;
            text-align: center;
            font-weight: 400;
            margin-top: 1.5vh;
        }

        .main__driving-question-selection-grid {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 1vh;
            margin-top: 1vh;
        }

        .main__driving-question-selection-item {
            width: 100%;
            padding: 1vh;
            border-radius: 0.4vh;
            background: radial-gradient(96.74% 132.31% at 49.76% 0%, rgba(57, 113, 217, 0.15) 0%, rgba(57, 113, 217, 0) 100%);
            display: flex;
            align-items: center;
            gap: 1vh;
            color: var(--color-white);
            cursor: pointer;
            transition: 0.2s ease-in;
            border: 0.1vh solid transparent;
        }

        .main__driving-question-selection-item:hover {
            border: 0.1vh solid #3971d9;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0) 100%);
            box-shadow: 0 0 3.7vh #3971d9 inset, 0 4px 56px #3971d940;
        }

        .main__driving-question-selection-item:hover p {
            text-shadow: 0vh 0vh 1.7vh #9575ac30;
        }

        .main__driving-question-selection-item-flexbox {
            width: 100%;
            display: flex;
            gap: 2vh;
            align-items: center;
        }

        .main__driving-question-selection-item-number {
            width: 4vh;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.2vh;
            border: 0.1vh solid #3971d9;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0) 100%);
            box-shadow: 0 0 3.7vh #3971d9 inset, 0 4px 56px #3971d940;
            color: var(--color-white);
        }

        .main__driving-question-selection-item-answer {
            font-size: 1.4vh;
        }

        .main__driving-question-timeline-container {
            width: 51.2vh;
            border-radius: 0.2vh;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0) 100%);
            box-shadow: 0 0.4vh 5.6vh #3971d940;
            padding: 0.5vh 0.5vh 0.5vh 1vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1vh;
        }

        .main__driving-question-timeline-top-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__driving-question-timeline-top-left-header {
            display: flex;
            gap: 1vh;
            align-items: center;
        }

        .main__driving-question-timeline-top-left-header p,
        .main__driving-question-timeline-top-right-header p {
            color: white;
            font-size: 1.5vh;
            font-weight: 400;
        }

        .main__driving-question-timeline {
            position: relative;
            width: 15vh;
            height: 0.35vh;
            background: #ffffff30;
            border-radius: 5vh;
        }

        .main__driving-question-timeline-btn {
            width: 12vh;
            height: 3.2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 0 0.4vh #f65454;
            filter: drop-shadow(0vh 0.4vh 2.9vh rgba(246, 84, 84, 0.55));
            background: #f65454;
            font-size: 1.2vh;
            color: var(--color-white);
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__driving-question-timeline-btn:hover {
            box-shadow: 0 0 1.7vh #f65454;
        }

        .main__driving-question-timeline-timer {
            width: 12vh;
            height: 3.2vh;
            background: rgba(66, 255, 107, 0.15);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: var(--ff-header);
            font-stretch: expanded;
            font-size: 1.2vh;
            color: #42ff6b !important;
            text-shadow: 0vh 0vh 1.7vh #42ff6b;
            font-weight: 500;
        }

        .main__driving-question-timeline-fill {
            width: 70%;
            height: 100%;
            background: #60e2ff;
            border-radius: 5vh;
        }

        .main__driving-question-bg-stripes-container {
            position: absolute;
            top: -18.5vh;
            right: -8vh;
        }

        .main__driving-question-bg-stripes-container img {
            width: 50vh;
        }

        .main__driving-question-bg-img-container {
            position: absolute;
            width: 20vh;
            height: 20vh;
            right: 7vh;
            top: 0vh;
        }

        .main__driving-question-bg-img-container img {
            width: 100%;
            height: 100%;
        }

        .main__driving-question-main-header {
            padding: 2vh;
            border-radius: 0.4vh;
            background: radial-gradient(83.27% 113.89% at 49.76% 0%, rgba(57, 113, 217, 0.15) 0%, rgba(57, 113, 217, 0) 100%);
            text-align: center;
            margin-top: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .main__driving-question-main-header-title {
        font-family: var(--ff-druk);
        font-size: 2vh;
        text-transform: uppercase;
        color: #3971d9;
        }
        .main__driving-question-main-header-desc {
        font-size: 1.2vh;
        color: #ffffff80;
        width: 80%;
        padding-top: 1vh;
        }

        .main__driving-congrats-container,
        .main__driving-failed-container {
            width: 100%;
            height: 55vh;
        }

        .main__driving-succes-bg-img,
        .main__driving-miss-bg-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
        }

        .main__driving-succes-bg-img img,
        .main__driving-miss-bg-img img {
            width: 100vh;
        }

        .main__driving-success-img,
        .main__driving-miss-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .main__driving-success-img img,
        .main__driving-miss-img img {
            width: 50vh;
            margin-top: -10vh;
        }

        .main__driving-success-text-container,
        .main__driving-miss-text-container {
            width: 50vh;
            position: absolute;
            bottom: 12vh;
            left: 50%;
            transform: translate(-50%);
            text-align: center;
        }

        .main__driving-success-text-title,
        .main__driving-miss-text-title {
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, #3971d9 0%, #000 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 3.7vh;
            font-family: var(--ff-druk);
            text-transform: uppercase;
            font-weight: 500;
        }

        .main__driving-success-text-desc,
        .main__driving-miss-text-desc {
            font-size: 1.2vh;
            color: #ffffff80;
        }

        .main__driving-failes-img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin-top: 4.2vh;
        }

        ::-webkit-scrollbar {
            width: .3vh;
        }

        ::-webkit-scrollbar-thumb {
            width: .3vh;
            background: #9b48d6;
        }

        ::-webkit-scrollbar-track {
            width: .3vh;
            background: #9b48d61F;
        }

        .fa-check.accept {
            color: #3bd738;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .fa-times.decline {
            color: #ff4646;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__hud-top-container-information {
            position: absolute;
            top: 3vh;
            right: 3vh;
            z-index: -2;
            font-family: var(--ff-proximanova) !important;
            font-weight: 600;
            transform: skew(-10deg);
            text-transform: uppercase;
            font-size: 2.8vh;
            color: var(--clr-white);
            text-shadow: 0.35vh 0.35vh 0vh rgba(0, 0, 0, 0.25);
            text-shadow: 0vh 0vh 1.7vh var(--clr-white);
            letter-spacing: 0.6vh;
        }

        .main__hud-top-container-information p span {
            color: var(--clr-new-orange);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
        }

        .main__hud-top-header-stars {
            position: absolute;
            top: 0.5vh;
            right: 2.9vh;
            color: #ff2323;
        }

        .far .fa-starr.active:before,
        .fas .fa-starr.active:before {
            color: #ff2323
        }

        .main__hud-top-big-tower-container,
        .main__hud-top-big-mic-container {
            width: 3.8vh;
            height: 3.8vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .main__hud-top-big-tower-phase-1 {
            width: 2.533vh;
            height: 2.533vh;
            background-image: url(./img/hud/tower-phase-1.svg);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: contain;
        }

        .main__hud-top-big-tower-phase-2 {
            width: 3.167vh;
            height: 3.167vh;
            background-image: url(./img/hud/tower-phase-2.svg);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: contain;
        }

        .main__hud-top-big-tower-phase-3 {
            width: 3.8vh;
            height: 3.8vh;
            background-image: url(./img/hud/tower-phase-3.svg);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: contain;
        }

        .main__hud-top-big-tower-phase-1.active {
            background-image: url(./img/hud/tower-phase-1-green.svg);
        }

        .main__hud-top-big-tower-phase-2.active {
            background-image: url(./img/hud/tower-phase-2-green.svg);
        }

        .main__hud-top-big-tower-phase-3.active {
            background-image: url(./img/hud/tower-phase-3-green.svg);
        }

        .main__hud-top-big-mic-phase-1 {
            width: 2.533vh;
            height: 2.533vh;
            background: url(./img/hud/mic-phase-1.svg);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: contain;
        }

        .main__hud-top-big-mic-phase-2 {
            width: 3.167vh;
            height: 3.167vh;
            background: url(./img/hud/mic-phase-2.svg);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: contain;
        }

        .main__hud-top-big-mic-phase-3 {
            width: 3.8vh;
            height: 3.8vh;
            background: url(./img/hud/mic-phase-3.svg);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-size: contain;
        }

        .main__hud-top-big-tower-icon img {
            width: 1.2vh;
        }

        .main__hud-top-big-mic-icon img {
            width: 1.5vh;
        }

        .main__hud-top-big-id-container {
            width: 7vh;
        }

        .main__hud-top-big-id-container-grid {
            width: 100%;
            display: grid;
            grid-template-columns: .5fr 1fr;
        }

        .main__hud-top-big-id {
            font-size: 1.4vh;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 1);
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__hud-top-big-id-icon {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__hud-top-big-id-icon img {
            width: 1.2vh;
        }

        .main__hud-top-big-grid {
            position: absolute;
            top: 6.6vh;
            right: 3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5vh;
            z-index: -2;
        }

        .main__hud-top-id-text {
            color: rgba(255, 255, 255, 1);
        }

        .right__id-container img {
            width: 1.5vh;
        }

        .right__id-container span {
            font-size: 1.25vh;
        }

        .main__top-right-speech-container {
            display: flex;
            justify-content: left;
            align-items: center;
            height: 2vh;
            width: 100%;
            gap: .2vh;
            margin-right: .2vh;
        }

        .dot,
        .radio {
            width: 1.5vh;
            transform: skew(-10deg);
            background: rgba(255, 255, 255, 0.15);
            height: 0.5vh;
        }
        
        .dot.active,
        .radio.active {
            width: 1.5vh;
            transform: skew(-10deg);
            background: var(--clr-blue);
            box-shadow: 0 0 1.7vh var(--clr-blue);
            height: 0.5vh;
        }
        
        .right__speech-container {
            display: flex;
            justify-content: left;
            align-items: center;
            gap: 0.5vh;
        }

        .right__id-container,
        .main__hud-right-radio,
        .main__hud-right-mic {
            padding: .5vh;
            display: flex;
            align-items: center;
            gap: 0.5vh;
            font-size: 1.25vh;
            color: rgba(255, 255, 255, .5);
            height: 2.5vh;
            border-radius: .25vh;
            border: .1vh solid rgba(255, 255, 255, .2);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .2) inset;
        }

        .right__id-container.dark,
        .main__hud-right-radio.dark,
        .main__hud-right-mic.dark {
            border: .1vh solid rgba(0, 0, 0, .5) !important;
            color: rgba(255, 255, 255);
            box-shadow: 0vh 0vh 1.7vh rgba(0, 0, 0, 0.75) inset !important;
        }
        
        .fa-microphone {
            color: white;
            font-size: 1.3vh;
            transition: .2s ease-in;
        }
        
        .fa-microphone.talking {
            color: var(--clr-money-green);
        }
        
        .fa-walkie-talkie {
            color: white;
            font-size: 1.5vh;
        }

        .fa-walkie-talkie.connected {
            color: var(--clr-red);
        }
        
        .fa-microphone.muted,
        .fa-walkie-talkie.muted {
            color: rgb(202, 42, 42);
            text-shadow: 0vh 0vh 1vh rgb(202, 42, 42);
        }

        .main__hud-top-money-grid {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5vh;
        }

        .main__hud-top-money-container {
            position: absolute;
            height: 3.3vh;
            top: 10vh;
            right: 3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: -2;
        }

        .main__hud-top-money-container.bank__money,
        .main__hud-top-money-container.black__money {
            top: 13vh;
        }

        .main__hud-top-money-plus {
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #252525;
            font-weight: 500;
            padding-top: .2vh;
            padding-bottom: .2vh;
            padding-right: .5vh;
            padding-left: .5vh;
            background: #542d7d;
            border-radius: .5vh;
        }

        .main__hud-top-money-pocket {
            font-size: 2vh;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 1);
            font-weight: 700;
        }

        .main__hud-top-black-money-container {
            position: absolute;
            top: 12.3vh;
            right: 3vh;
            font-size: 1.4vh;
            font-weight: 500;
            color: #FF4646;
            z-index: -2;
        }

        .main__hud-top-ammonation-container {
            position: absolute;
            top: 17vh;
            right: 3vh;
            z-index: -2;
            display: flex;
            align-items: center;
            gap: 1vh;
            justify-content: flex-end;
            animation: ammo 0.3s linear;
        }

        @keyframes ammo {
            0% {
                right: -10vh;
            }
            100% {
                right: 3vh;
            }
        }

        .main__hud-top-ammonation-weapon-name {
            font-size: 1.25vh;
            color: var(--clr-white);
            text-align: right;
            margin-right: -3.7vh;
            margin-top: -1.5vh;
        }

        .main__hud-top-ammonation-weapon-name p:first-child {
            border-bottom: 0.1vh solid rgba(255, 255, 255, 0.25);
        }

        .main__hud-top-ammonation-weapon-ammo-count {
            text-transform: uppercase;
            display: flex;
            justify-content: right;
            align-items: right;
            font-size: 1vh;
            color: #ffffff80;
            margin-top: 2.2vh;
        }

        .main__hud-top-ammonation-img {
            width: 2.5vh;
            height: 2.5vh;
            border-radius: 0.25vh;
            border: 0.1vh solid var(--clr-weapon-border);
            box-shadow: 0 0 1.7vh var(--clr-weapon-box-shadow) inset;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.5vh;
        }

        .main__hud-top-ammonation-img img {
            width: 1.5vh;
        }

        .main__hud-carhud-container {
            position: absolute;
            bottom: 4vh;
            right: 4vh;
            transform: skew(-7.5deg);
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__hud-carhud-rpm {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            align-content: flex-end;
            justify-content: flex-end;
            gap: 0.5vh;
        }

        .main__hud-carhud-rpm-item {
            width: 3vh;
            height: 1vh;
            background: rgba(255, 255, 255, 0.1);
        }

        .main__hud-carhud-flex-top {
            display: flex;
            align-items: flex-end;
        }

        .main__hud-carhud-flex-top-left {
            font-family: var(--ff-body);
            font-size: 5vh;
            font-weight: 700;
            color: #fff;
            text-shadow: 0vh 0vh 3vh rgba(0, 0, 0, 0.5);
            text-shadow: 0.25vh 0.5vh 0vh rgba(0, 0, 0, 0.5);
        }

        .main__hud-carhud-flex-bottom {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__hud-carhud-flex-bottom-item {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3vh;
            height: 3vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5vh;
            font-size: 1.25vh;
            color: #ffffff80;
        }

        .main__hud-carhud-flex-bottom-item.active {
            color: var(--clr-new-orange);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
        }

        .main__hud-carhud-flex-bottom-item.active2 {
            color: #20ef41;
            text-shadow: 0vh 0vh 1.7vh #20ef41;
        }

        .main__hud-carhud-flex-bottom-item.active3 {
            color: var(--clr-new-blue);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-blue);
        }

        .main__hud-carhud-flex-bottom-item-stripe {
            position: absolute;
            bottom: -0.75vh;
            width: 2.5vh;
            height: 0.15vh;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5vh;
        }

        .main__hud-carhud-flex-bottom-item-stripe.active {
            background: var(--clr-new-orange);
            box-shadow: 0 0 1.7vh var(--clr-new-orange);
        }

        .main__hud-carhud-flex-bottom-item-stripe.active2 {
            background: #20ef41;
            box-shadow: 0 0 1.7vh #20ef41;
        }

        .main__hud-carhud-flex-bottom-item-stripe.active3 {
            background: var(--clr-new-blue);
            box-shadow: 0 0 1.7vh var(--clr-new-blue);
        }

        .main__hud-carhud-flex-top-left p span {
            font-size: 1.5vh;
            font-weight: 500;
        }

        .main__hud-carhud-fuel-container {
            display: flex;
            align-items: center;
            gap: 1vh;
            margin-bottom: 1vh;
        }

        .main__hud-carhud-fuel-icon {
            height: 3vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            padding-left: 1vh;
            padding-right: 1vh;
            gap: 1vh;
            border-radius: 0.5vh;
            font-size: 1.6vh;
        }

        .main__hud-carhud-fuel-icon p {
            color: #ffffff80;
            font-size: 1.6vh;
            font-weight: 500;
            font-family: var(--ff-rad);
        }

        .main__hud-carhud-fuel-progress {
            width: 20vh;
            height: 0.25vh;
            background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 5vh;
        }

        .main__hud-carhud-fuel-progress-fill {
            width: 50%;
            height: 100%;
            background: #20ef41;
            box-shadow: 0 0 1.7vh #20ef41;
            border-radius: 5vh;
            transition: width 0.3s ease-in-out;
        }
        
        .main__hud-carhud-rpm-item.width-7,
        .main__hud-carhud-rpm-item.width-6,
        .main__hud-carhud-rpm-item.width-5,
        .main__hud-carhud-rpm-item.width-4,
        .main__hud-carhud-rpm-item.width-3,
        .main__hud-carhud-rpm-item.width-2,
        .main__hud-carhud-rpm-item.width-1 {
            width: 2vh;
        }

        .main__hud-carhud-rpm-item.active {
            background: var(--clr-new-orange);
            box-shadow: 0 0 1.7vh var(--clr-new-orange);
        }
        .main__hud-carhud-flex-top-right-gear {
            position: absolute;
            right: 0;
            bottom: 5vh;
            display: flex;
            align-items: center;
            gap: 1vh;
            height: 3vh;
            padding-left: 1vh;
            padding-right: 1vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5vh;
            font-size: 1.5vh;
        }

        .main__hud-carhud-flex-top-right-gear-icon {
            color: var(--clr-new-blue);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-blue);
        }

        .main__hud-location-container {
            display: flex;
            align-items: center;
            gap: 1vh;
            font-size: 1.5vh;
            color: #fff;
            position: absolute;
            top: 83.5vh;
            left: 29.5vh;
        }

        .main__hud-job-container {
            display: flex;
            align-items: center;
            gap: 1vh;
            font-size: 1.5vh;
            color: #fff;
            position: absolute;
            top: 79.9vh;
            left: 29.5vh;
        }

        .main__hud-bottom-information-item {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__hud-bottom-information-item-icon {
            width: 3vh;
            height: 3vh;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            border-radius: 0.25vh;
            color: var(--clr-white);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            font-size: 1.25vh;
        }

        .main__hud-bottom-information-item-icon.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }

        .main__hud-bottom-information-item-text {
            line-height: 0;
            font-size: 1.25vh;
        }

        .main__hud-bottom-information-item-text p:last-child {
            color: #ffffff80;
            margin-top: 1.5vh;
            font-size: 1.25vh;
        }

        .main__hud-clock-container {
            position: absolute;
            top: 2vh;
            left: 2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5vh;
            z-index: -2;
        }

        .main__hud-clock-item {
            display: flex;
            align-items: center;
            gap: 1vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.2);
            border-radius: 0.25vh;
            box-shadow: 0 0 1.7vh #fff3 inset;
            padding: 0.5vh;
            height: 2.5vh;
            transition: .2s ease-in;
            font-size: 1.25vh;
            color: var(--clr-blue);
        }

        .main__hud-clock-item i {
            text-shadow: 0vh 0vh 1vh var(--clr-blue);
        }

        .main__hud-clock-item.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }

        .main__hud-clock-time,
        .main__hud-clock-date,
        .main__hud-clock-team {
            font-size: 1.25vh;
            color: var(--clr-white);
            font-weight: 600;
        }

        .main__hud-teamstats-container {
            position: absolute;
            top: 5vh;
            left: 2.5vh;
            display: flex;
            flex-direction: column;
            align-items: right;
            gap: 0.5vh;
            z-index: -2;
        }

        .main__hud-teamstats-item {
            display: flex;
            align-items: center;
            gap: 1vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.2);
            border-radius: 0.25vh;
            box-shadow: 0 0 1.7vh #fff3 inset;
            padding: 0.5vh;
            height: 2vh;
            font-size: 1vh;
            transition: .2s ease-in;
            color: var(--clr-blue);
        }

        .main__hud-teamstats-item i {
            text-shadow: 0vh 0vh 1vh var(--clr-blue);
        }

        .main__hud-teamstats-item.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }

        .main__hud-teamstats-einreise,
        .main__hud-teamstats-guids {
            font-size: 1vh;
            color: var(--clr-white);
            font-weight: 600;
        }

        .main__hud-press-e-container-wrapper {
            position: absolute;
            bottom: 2vh;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .main__hud-press-e-container {
            width: 5.5vh;
            height: 5.5vh;
            border-radius: 50%;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
            padding: 0.25vh;
            margin-left: 3.5vh;
        }

        .main__hud-press-e-circle {
            width: 100%;
            height: 100%;
            border: 0.2vh solid var(--clr-new-2-orange);
            border-radius: 50%;
            box-shadow: 0vh 0vh 1.7vh var(--clr-new-2-orange), 0vh 0vh 1.7vh var(--clr-new-2-orange) inset;
            padding: 0.5vh;
        }

        .main__hud-press-e-circle-2 {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 0.1vh solid rgba(255, 255, 255, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.5vh;
            font-family: var(--ff-rad);
            font-weight: bold;
            color: var(--clr-new-2-orange);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-2-orange);
        }

        .main__hud-press-e-circle-2 p {
            transform: skew(-15deg);
        }

        .main__hud-press-text {
            color: var(--clr-white);
            width: 13vh;
            font-size: 1vh;
            text-align: center;
            margin-top: 0.5vh;
        }


        .main__hud-deathtimeout-death-timeout {
            display: none;
            position: absolute;
            width: 20vh;
            color: #ffffff;
            bottom: 24.94vh;
            left: 2.5vh;
        }

        .main__hud-deathtimeout-text {
            font-family: 'Bebas Neue';
            font-style: normal;
            font-weight: 400;
            font-size: 19.59px;
            line-height: 24px;
            color: #EFA064;
            text-shadow: 0px 0px 11.4px rgba(241, 171, 111, 0.57);
        }
        
        .main__hud-deathtimeout-time {
            font-family: 'Bebas Neue';
            font-style: normal;
            font-weight: 400;
            font-size: 19.59px;
            color: rgba(255, 255, 255, 0.65);
            text-shadow: 0px 0px 9.9px rgba(255, 255, 255, 0.45);
            margin-left: 0.5vh;
        }

        .main__notify-container {
            display: flex;
            flex-direction: column;
            gap: 1vh;
            z-index: -2;
            position: absolute;
            left: 2vh;
            top: 10vh;
            width: 23vh;
            height: 40vh;
            overflow: hidden;
        }
        
        .main__lifeinvader-notify-container {
            position: absolute;
            bottom: 25vh;
            left: 1vh;
            width: 28vh;
            height: 23vh;
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            justify-content: flex-end;
            align-content: flex-end;
            gap: 1vh;
            overflow: hidden;
            z-index: -2;
        }
        
        .main__notify-item {
            position: relative;
            width: 100%;
            padding: 1vh;
            border: 0.2vh solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 10vh #fff3 inset;
            border-radius: 0.25vh;
            margin-top: 1.5vh;
        }

        .main__notify-item.dark {
            border: 0.2vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 10vh #000000bf inset !important;
        }

        .main__notify-item.success {
            border: 0.1vh solid var(--clr-notify-item-success);
            box-shadow: 0 0 4vh var(--clr-notify-item-success) inset;
        }

        .main__notify-item.error {
            border: 0.1vh solid var(--clr-notify-item-error);
            box-shadow: 0 0 4vh var(--clr-notify-item-error) inset;
        }

        .main__notify-item:first-child {
            margin-top: 1vh;
        }
        
        .main__life-notify-item {
            position: relative;
            width: 100%;
            padding: 1.5vh;
            border-top-left-radius: 1vh;
            border-top-right-radius: 1vh;
            margin-top: 1vh;
            animation: .5s fadeinleft;
        }

        .main__life-notify-bg {
            padding: 1.5vh;
            border-top-left-radius: 1vh;
            border-top-right-radius: 1vh;
            background: linear-gradient(to right, #FC4546, rgba(0, 0, 0, 0));
        }

        .main__life-notify-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: var(--clr-white);
            font-weight: 700;
            margin-top: -0.5vh;
        }
        
        .main__life-notify-new-ad {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main__life-notify-phone {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to right, #C52C2B, rgba(0, 0, 0, 0));
            border-radius: 0.7vh;
            padding: 0.7vh;
            margin-top: 0.5vh;
        }
        
        .main__life-notify-phone-header {
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        
        .main__life-notify-phone-header img {
            width: 1vh;
            margin-left: 0.5vh;
        }
        
        .main__life-notify-phone-header p {
            font-size: 1.4vh;
            font-weight: 500;
            color: var(--clr-white);
        }
        
        .main__life-notify-phone-number {
            font-size: 1.4vh;
            font-weight: 500;
            text-transform: uppercase;
            color: var(--clr-white);
        }
        
        .main__life-notify-content {
            width: 100%;
            display: flex;
            gap: 0.5vh;
            margin-top: 0.5vh;
            background: linear-gradient(to right, #0F0F0F, rgba(0, 0, 0, 0));
            border-bottom-left-radius: 1vh;
            border-bottom-right-radius: 1vh;
            border-top: 0.2vh solid red;
            padding: 1vh;
        }
        .main__life-notify-content-text {
            font-size: 1.4vh;
            color: rgba(255, 255, 255, 0.466);
            font-weight: 500;
        }
        
        .main__life-notify-new-ad-text {
            font-size: 1.4vh;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.55);
        }
        
        .main__life-notify-new-ad-person {
            font-size: 1.4vh;
            font-weight: 600;
            color: var(--clr-white);
        }
        
        .main__notify-top-icon {
            position: absolute;
            top: 0vh;
            left: 2vh;
            margin-top: -1.25vh;
        }
        
        .main__notify-ooc-sphere {
            position: absolute;
            top: 5vh;
            right: -7vh;
            width: 8.8vh;
            height: 8.8vh;
            border-radius: 50%;
            background: #8c00ff;
            filter: blur(6vh);
            animation: 5s bluemovment;
        }
        
        .main__notify-info-sphere {
            position: absolute;
            top: 5vh;
            right: -7vh;
            width: 8.8vh;
            height: 8.8vh;
            border-radius: 50%;
            background: #8c00ff;
            filter: blur(6vh);
            animation: 5s bluemovment;
        }
        
        .main__notify-success-sphere {
            position: absolute;
            top: 5vh;
            right: -7vh;
            width: 8.8vh;
            height: 8.8vh;
            border-radius: 50%;
            background: #8c00ff;
            filter: blur(6vh);
            animation: 5s bluemovment;
        }
        
        .main__notify-error-sphere {
            position: absolute;
            top: 5vh;
            right: -7vh;
            width: 8.8vh;
            height: 8.8vh;
            border-radius: 50%;
            background: #8c00ff;
            filter: blur(6vh);
            animation: 5s bluemovment;
        }
        
        .main__notify-teamchat-sphere {
            position: absolute;
            top: 5vh;
            right: -7vh;
            width: 8.8vh;
            height: 8.8vh;
            border-radius: 50%;
            background: #8c00ff;
            filter: blur(6vh);
            animation: 5s bluemovment;
        }
        
        .main__notify-red-sphere {
            position: absolute;
            top: 5vh;
            right: -7vh;
            width: 8.8vh;
            height: 8.8vh;
            border-radius: 50%;
            background: #8c00ff;
            filter: blur(6vh);
            animation: 5s bluemovment;
        }
        
        @keyframes bluemovment {
            0% {
                
            }
        
            100% {
                margin-left: -5vh;
                margin-top: 2vh;
            }
        }
        
        .main__notify-white-sphere {
            position: absolute;
            top: -9vh;
            left: -9vh;
            width: 14vh;
            height: 14vh;
            border-radius: 50%;
            background: rgba(255, 255, 255, 1);
            filter: blur(12vh);
            animation: 5s whitemovement;
        }
        
        @keyframes whitemovement {
            0% {
        
            }
        
            100% {
                margin-left: 15vh;
                margin-top: 1vh;
            }
        }
        
        .main__teamchat-header {
            font-size: 1.8vh;
            font-weight: 700;
            color: rgba(255, 255, 255, 1);
            margin-top: 1vh;
        }

        .main__notify-header {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--clr-white);
        }
        
        .main__notify-content,
        .main__teamchat-content {
            font-size: 1.4vh;
            color: #FFFFFF8C;
            font-weight: 500;
        }
        
        .main__teamchat-progress {
            margin-top: 1vh;
            width: 100%;
            background: #0000004A;
            height: .2vh;
        }

        .main__notify-progress {
            width: 100%;
            height: 0.5vh;
            transform: skew(-10deg);
            background: rgba(255, 255, 255, 0.15);
            margin-top: 0.8vh;
        }
        
        .main__notify-progress-fill {
            width: 100%;
            height: 100%;
            background: var(--clr-notify-item-info);
            box-shadow: 0 0 1.7vh var(--clr-notify-item-info);
            animation: 7s notifyprogress linear;
        }
        
        .main__notify-progress-fill.success {
            background: var(--clr-notify-item-success);
            box-shadow: 0 0 1.7vh var(--clr-notify-item-success);
        }
        
        .main__notify-progress-fill.error {
            background: var(--clr-notify-item-error);
            box-shadow: 0 0 1.7vh var(--clr-notify-item-error);
        }
        
        .main__teamchat-progress-fill {
            background: #ddff00;
            box-shadow: 0vh 0vh 3.4vh #ddff00;
            animation: 6s notifyprogress linear;
        }

        .main__hud-announce-progress {
            width: 100%;
            height: 0.5vh;
            transform: skew(-10deg);
            background: rgba(255, 255, 255, 0.15);
            margin-top: 1vh;
        }
        
        .main__hud-announce-progress-fill {
            width: 70%;
            height: 100%;
            background: var(--clr-announce-item);
            box-shadow: 0 0 1.7vh var(--clr-announce-item);
            animation: 7s notifyprogress linear;
        }
        
        @keyframes notifyprogress {
            0% {
                width: 0%;
            }
        
            100% {
                width: 100%;
            }
        }

        .main__notify-grid {
            display: grid;
            grid-template-columns: .2fr 1fr;
            width: 100%;
            gap: 1vh;
        }
        
        .main__nofity-icon-container {
            display: flex;
            justify-content: center;
            display: flex;
            align-items: center;
            gap: 1vh;
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--clr-white);
            background: var(--clr-notify-item-info);
            width: 2.2vh;
            height: 2.2vh;
            padding: 0.5vh;
            border-radius: 0.25vh;
            margin-top: -2vh;
            box-shadow: 0 0 1.7vh var(--clr-notify-item-info);
            color: #00000080;
        }

        .main__nofity-icon-container.error {
            background: var(--clr-notify-item-error);
            box-shadow: 0 0 1.7vh var(--clr-notify-item-error);
        }

        .main__nofity-icon-container.success {
            background: var(--clr-notify-item-success);
            box-shadow: 0 0 1.7vh var(--clr-notify-item-success);
        }
        
        .main__nofity-icon-container i {
            font-size: 1.5vh;
            color: black;
            opacity: 52%;
            text-shadow: 0vh 0vh 1vh black;
        }
        
        .main__nofity-text-container {
            width: 120%;
            display: flex;
            justify-content: center;
            align-items: left;
            flex-direction: column;
            font-size: 1.16vh;
            margin-left: -4.5vh;
            margin-top: 0.6vh;
        }
        
        .main__notify-text-title {
            color: var(--clr-white);
            font-weight: 600;
            font-size: 1.3vh;
            text-transform: uppercase;
        }
        
        .main__notify-text-description {
            color: rgba(255, 255, 255, 0.853);
            font-weight: 500;
            margin-top: 0.4vh;
        }

        .main__notify-grid {
            display: grid;
            grid-template-columns: .2fr 1fr;
            width: 100%;
            gap: 1vh;
        }

        .main__food-drink-container {
            position: absolute;
            bottom: 3vh;
            left: 16vh;
            width: 28vh;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 3vh;
        }

        .main__food-container,
        .main__drink-container {
            position: relative;
            width: 14.5vh;
            padding: 0.5vh;
            height: 2vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            border-radius: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: 0.2s ease-in;
        }
        
        .main__food-container.dark,
        .main__drink-container.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }
        
        .main__food-drink-text {
            position: absolute;
            top: -2.5vh;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.3vh;
            color: white;
            pointer-events: none;
        }

        .main__drink-container-stripes,
        .main__food-container-stripes {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: .5vh;
            width: 13vh;
        }

        .main__drink-stripes,
        .main__food-stripes {
            width: 100%;
            height: 0.5vh;
            transform: skew(-10deg);
            background: rgba(255, 255, 255, 0.15);
        }

        .main__drink-stripes.active {
            background: var(--clr-foodhud-thirst-bg);
            box-shadow: 0 0 1.7vh var(--clr-foodhud-thirst-box-shadow);
        }

        .main__food-stripes.active {
            background: var(--clr-foodhud-food-box-shadow);
            box-shadow: 0 0 1.7vh var(--clr-foodhud-food-box-shadow);
        }

        .main__food-drink-text {
            position: absolute;
            top: -2.5vh;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.3vh;
            color: white;
            pointer-events: none;
        }

        .main__food-header,
        .main__drink-header {
            text-align: left;
        }

        .main__food-text,
        .main__drink-text {
            text-align: right;
        }

        .main__hud-mute-container {
            position: absolute;
            top:90%;
            left :47.4%;
            transform:translate(-50%, -50%);
        }

        .main__hud-mute-box {
            position: absolute;
            height: 11.4vh;
            width: 11.4vh;
            border: 0.2vh solid transparent;
            border-radius: 60%;
            animation: muteani 0.5s linear;
            transform: rotate(90deg) scale(0.7);
            filter:drop-shadow(0vh 0vh 1.4vh rgb(0, 0, 0));
            zoom:0.84
        }
        
        @keyframes muteani {
            0% {
                top: 18vh;
            }
            100% {
                top: 0vh;
            }
        }
        
        .main__hud-mute-outer-circle {
            position: absolute;
            width: 9.4vh;
            height: 9.4vh;
            top: 0.8vh;
            bottom: 2vh;
            left: 0.74vh;
            background: #ffffff15;
            border-radius: 50%;
            -webkit-animation: spin 1.5s linear infinite;
            animation: spin 1.5s linear infinite;
            filter: drop-shadow(0vh 0vh 1.4vh #fc3333);
        }
        
        .main__hud-mute-inner-circle {
            position: absolute;
            width:8vh;
            height:8vh;
            top: 1.48vh;
            bottom: 2vh;
            left: 1.44vh;
            color:white;
            border: 0.6vh solid #fc3333;
            border-radius: 50%;
            -webkit-animation: spin 1.5s linear infinite;
            animation: spin 1.5s linear infinite;
            filter: drop-shadow(0vh 0vh 1.4vh #fc3333);
        }
        
        .main__hud-mute-img {
            position: absolute;
            width: 5.74vh;
            height: 5.74vh;
            top: 2.6vh;
            left: 2.5vh;
            line-height: 5.2vh;
            border: 0.29vh solid #ffffff5d;
            border-radius: 50%;
            transform: rotate(-90deg);
            text-shadow:0vh 0vh 0.8vh #ffffff5d;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__hud-mute-img img {
            width: 4.5vh;
        }

        .main__teamchat-container {
            position: absolute;
            top: 25vh;
            right: 2vh;
            width: 23vh;
            height: 46vh;
            display: flex;
            flex-direction: column;
            gap: 2vh;
            overflow: hidden;
        }

        .main__hud-announce-container {
            position: absolute;
            top: 2vh;
            left: 50%;
            transform: translate(-50%);
            width: 40vh;
            display: flex;
            flex-direction: column;
            gap: 2vh;
        }

        .main__hud-announce-item {
            width: 100%;
            padding: 1vh;
            border: 0.2vh solid rgba(255, 255, 255, 0.2) ;
            box-shadow: 0 0 10vh #fff3 inset;
            border-radius: 0.25vh;
            font-size: 1.5vh;
            text-align: center;
            margin-top: 1vh;
        }

        .main__hud-announce-item.dark {
            border: 0.2vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 10vh #000000bf inset !important;
        }

        .main__teamchat-item {
            animation: .5s fadeinright;
        }

        @keyframes fadeinright {
            0% {
                right: -30vh;
            }

            100% {
                right: 0vh;
            }
        }
    
        .main__notify-item {
            animation: .5s fadeinleft;
        }

        @keyframes fadeinleft {
            0% {
                left: -30vh;
            }

            100% {
                left: 0vh;
            }
        }

        @keyframes fadeintop {
            0% {
                top: -30vh;
            }

            100% {
                top: 0vh;
            }
        }

        @keyframes announcemovein {
            0% {
                top: -20vh;
                opacity: 0%;
            }
        
            100% {
                top: 3vh;
                opacity: 100%;
            }
        }

        .main__hud-announce-item {
            animation: .5s fadeintop;
            animation: 1s announcemovein;
        }

        .main__hud-sphere-overflow-container {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            top: 0;
            left: 0;
            border-radius: 1vh;
        }

        .main__hud-notify-border-container {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            top: 0;
            left: 0;
            border-radius: 1vh;
            border: .1vh solid rgba(255, 255, 255, 0.03);
        }

        .main__hud-top-chat-container {
            position: absolute;
            top: 10.5vh;
            left: 2vh;
            width: 28vh;
            z-index: 2;
        }

        .main__hud-top-chat-input-container {
            position: relative;
            width: 100%;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            border-radius: 0.25vh;
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            padding: 0.5vh 0.5vh 0.5vh 1vh;
            transition: .2s ease-in;
        }

        .main__hud-top-chat-input-container.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }

        .main__hud-top-chat-input-container input {
            font-size: 1.4vh;
            color: rgba(255, 255, 255, 0.55);
            background: none;
            outline: none;
            border: none;
            font-family: 'Rajdhani', sans-serif;
            width: 85%;
        }

        .main__hud-top-chat-input-container input::placeholder {
            color: rgba(255, 255, 255, 0.55);
        }

        .main__hud-top-chat-input-img {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            right: 1vh;
            top: 50%;
            transform: translateY(-50%);
            width: 2vh;
            height: 2vh;
            background: var(--clr-blue);
            border-radius: 0.1vh;
            cursor: pointer;
            transition: .2s ease-in;
        }

        .main__hud-top-chat-input-img img {
            width: 1.3vh;
        }

        .main__hud-top-chat-input-img:hover {
            box-shadow: 0 0 1.7vh var(--clr-blue);
        }

        .main__hud-top-mic-icon {
            font-size: 1.2vh;
        }

        .main__hud-top-mic-range-container {
            display: flex;
            align-items: center;
            gap: .5vh;
        }

        .main__hud-top-mic-range-item {
            width: .75vh;
            height: .75vh;
            background: rgba(255, 255, 255, .25);
        }

        .main__hud-top-mic-container {
            display: flex;
            gap: 1vh;
            align-items: center;
        }

        .main__hud-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-size: cover;
        }

        .main__money-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__money-container p {
            font-size: 1.5vh;
            color: var(--clr-money-green);
            text-shadow: .25vh .25vh 0vh rgba(0, 0, 0, 0.216);
            font-family: var(--ff-rad);
        }

        .main__money-container.bank__money p,
        .main__money-container.black__money p {
            font-size: 1.5vh;
            color: var(--clr-white);
            font-family: var(--ff-rad);
        }

        .main__money-icon-container {
            width: 2.5vh;
            height: 2.5vh;
            border: 0.1vh solid var(--clr-money-green);
            box-shadow: 0 0 1.7vh var(--clr-money-green-icon) inset;
            border-radius: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2vh;
            color: var(--clr-money-green);
            padding: 1vh;
        }

        .main__money-icon-container img {
            width: 5vh;
        }

        .main__money-icon-container.bank__money,
        .main__money-icon-container.black__money {
            width: 2.5vh;
            height: 2.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 1.7vh #ffffff40 inset;
            border-radius: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2vh;
            color: var(--clr-white);
            padding: 1vh;
        }

        .main__money-icon-container.bank__money.dark,
        .main__money-icon-container.black__money.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }

        .main__money-icon-container.bank__money img,
        .main__money-icon-container.black__money img {
            width: 1.45vh;
        }

        
        .main__announce-icon-container {
            width: 5vh;
            height: 2.2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--clr-white);
            background: var(--clr-announce-item);
            box-shadow: 0 0 1.7vh var(--clr-announce-item);
            border-radius: 0.25vh;
            margin-top: -2vh;
            position: relative;
            left: 50%;
            transform: translate(-50%);
            font-size: 1.3vh;
        }
        
        .main__announce-text-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: left;
            flex-direction: column;
            font-size: 1.4vh;
        }

        .main__announce-text-container p:first-child {
            font-size: 1.4vh;
            color: var(--clr-white);
            text-transform: uppercase;
            margin-top: 0.5vh;
            font-weight: 600;
        }

        .main__announce-text-container p:last-child {
            font-size: 1.2vh;
            color: #ffffff80;
            font-weight: 500;
        }

        .main__teamchat-progress {
            margin-top: 0.5vh;
            width: 100%;
            height: 0.5vh;
            transform: skew(-10deg);
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            justify-content: flex-end;
        }

        .main__teamchat-progress-fill {
            width: 50%;
            height: 100%;
            background: var(--clr-teamchat-item);
            box-shadow: 0 0 1.7vh var(--clr-teamchat-item);
        }

        .main__teamchat-item {
            position: relative;
            text-align: right;
            width: 100%;
            padding: 1vh;
            border: 0.2vh solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 10vh #fff3 inset;
            border-radius: 0.25vh;
            font-size: 1.5vh;
            color: var(--clr-white);
            animation: 1s moveinanimationleft;
        }

        .main__teamchat-item:first-child {
            margin-top: 1vh;
        }

        @keyframes moveinanimationleft {
            0% {
                right: -30vh;
                opacity: 0%;
            }

            100% {
                right: 0vh;
                opacity: 100%;
            }
        }

        .main__teamchat-icon-container {
            position: absolute;
            top: -1vh;
            right: 1vh;
            width: 2.2vh;
            height: 2.2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--clr-white);
            font-size: 1.2vh;
            background: var(--clr-teamchat-item);
            box-shadow: 0 0 1.7vh var(--clr-teamchat-item);
            border-radius: 0.25vh;
        }

        .main__teamchat-text-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: left;
            flex-direction: column;
            margin-top: 1vh;
        }

        .main__teamchat-text-title {
            font-size: 1.3vh;
            color: var(--clr-white);
            margin-top: 0.25vh;
            text-transform: uppercase;
        }

        .main__teamchat-text-description {
            font-size: 1.15vh;
            color: #ffffff80;
            font-weight: 600;
        }
        
        .main__hud-progess-container {
            position: absolute;
            bottom: 2vh;
            left: 50%;
            transform: translate(-50%);
        }

        .main__hud-progress-top-information-container {
            width: 25vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.25vh;
            color: var(--clr-white);
        }

        .main__hud-progress-bottom-loading-container {
            width: 25vh;
            padding: 0.5vh;
            height: 2vh;
            border: 0.1vh solid rgba(0, 0, 0, 0.5);
            box-shadow: 0 0 1.7vh #000000bf inset;
            border-radius: 0.25vh;
            margin-top: 0.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.25vh;
            overflow: hidden;
            position: relative;
        }

        .main__hud-progress-bottom-loading-container.dark {
            border: 0.1vh solid rgba(0, 0, 0, 0.5) !important;
            box-shadow: 0 0 1.7vh #000000bf inset !important;
        }

        .main__hud-progress-bottom-loading-item {
            width: 100%;
            height: 0.5vh;
            background: rgba(255, 255, 255, 0.15);
            transform: skew(-10deg);
        }

        .main__hud-progress-bottom-loading-item.active {
            background: var(--clr-progress-inner);
            box-shadow: 0 0 1.7vh var(--clr-progress-inner);
        }

        .container-hud-settings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40vh;
            background: var(--clr-settings-bg);
            padding: 3vh;
            border-radius: 0.5vh;
            z-index: 100;
            color: var(--clr-white);
            height: 50vh;
        }
            
        .hud-settings-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .hud-settings-header p:first-child {
            font-family: var(--ff-oswald);
            font-weight: 700;
            font-size: 2vh;
            text-transform: uppercase;
            color: var(--clr-red);
            text-shadow: 0vh 0vh 1.7vh var(--clr-red);
        }
        .hud-settings-header p:first-child span {
            color: var(--clr-white);
            text-shadow: 0vh 0vh 1.7vh var(--clr-white);
        }
        .hud-settings-header p:last-child {
            color: #ffffff80;
            font-size: 1.25vh;
        }
        
        .container-hud-settings-actions {
            display: flex;
            width: 34vh;
            height: 34vh;
            flex-direction: column;
            align-items: center;
            gap: 1.0185vh;
            overflow: scroll;
            margin-top: 1vh;
        }
        
        .container-hud-settings-actions::-webkit-scrollbar {
            width: 0.3vh;
            border-radius: 5vh;
        }

        .container-hud-settings-actions::-webkit-scrollbar-track {
            width: 0.3vh;
            border-radius: 5vh;
            background: rgba(255, 255, 255, 0.05);
        }

        .container-hud-settings-actions::-webkit-scrollbar-thumb {
            width: 0.3vh;
            border-radius: 5vh;
            background: var(--clr-blue);
        }
        
        .box-hud-settings-action {
            width: 100%;
            border-radius: 0.25vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            padding: 1vh;
            margin-top: 0.5vh;
        }

        .box-hud-settings-action:first-child {
            margin-top: 0vh;
        }

        .main__hud-settings-scroll-item-header {
            font-size: 1.25vh;
            color: var(--clr-white);
        }

        .main__hud-settings-scroll-item-description {
            font-size: 1vh;
            color: #ffffff80;
        }

        .main__hud-settings-scroll-item-selection {
            width: 100%;
            margin-top: 1vh;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5vh;
        }

        .main__hud-setting-scroll-item-selection-btn {
            width: 100%;
            padding: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1vh;
            color: var(--clr-white);
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            cursor: pointer;
            transition: 0.2s ease-in;
            transform: skew(-10deg);
            background: none;
        }

        .main__hud-setting-scroll-item-selection-btn:hover,
        .main__hud-setting-scroll-item-selection-btn.active {
            border: 0.1vh solid var(--clr-settings-btn-active);
            box-shadow: 0 0 1.7vh var(--clr-settings-btn-active) inset;
        }

        .main__hud-settings-scroll-item-selection-2 {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5vh;
            margin-top: 1vh;
        }

        .main__hud-settings-scroll-item-selection-3 {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 0.5vh;
            margin-top: 1vh;
        }
        
        .main__hud-close-hudsettings {
            box-sizing: border-box;
            position: absolute;
            right: 0%;
            cursor: pointer;
            width: 25px;
            height: 25px;
            top: 0.5vh;
        }
        
        .main__hud-close-loop-icon {
            position: absolute;
            margin: auto auto;
            width: 100%;
            left: 0%;
            right: 0%;
            top: 0%;
            bottom: 0%;
            transition: .2s ease-in;
        }
        
        .main__hud-close-loop-icon:hover {
            opacity: .5;
        }
        
        .main__top-right-content {
            display: flex;
        }
        
        .main__top-right-button {
            margin-left: 0.5vh;
            margin-top: 1vh;
            font-size: 1.4vh;
            color: rgb(255, 255, 255);
            font-family: "Rajdhani";
            font-weight: 600;
            width: 100%;
            height: 3.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            border-radius: 0.2vh;
            cursor: pointer;
            transition: 0.2s ease-in;
        }
        
        .main__top-right-button:hover {
            border: 0.1vh solid var(--clr-settings-save-btn);
            box-shadow: 0 0 1.7vh var(--clr-settings-save-btn) inset;
        }
        
        .main__top-right-button#btn2:hover {
            border: 0.1vh solid var(--clr-settings-reset-btn);
            box-shadow: 0 0 1.7vh var(--clr-settings-reset-btn) inset;
        }

        .hudsettings-aktiv {
            display: none !important;
        }

        .main__fasocity-header-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__fasocity-header-left-stripe {
            width: 0.1vh;
            height: 3.5vh;
            background: rgba(255, 255, 255, 0.5);
            margin-right: 0.5vh;
        }

        .main__fasocity-header-left {
            font-family: var(--ff-arame);
            font-size: 1.5vh;
        }

        .main__fasocity-header-left p:first-child {
            font-weight: 100;
            color: #ffffff80;
            letter-spacing: 0.5vh;
            line-height: 1.5vh;
        }

        .main__fasocity-header-left P:last-child {
            font-weight: 700;
            font-size: 2.6vh;
            color: #fff;
            line-height: 2.6vh;
        }

        .main__fasocity-header-container-right {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__fasocity-header-container-right-back-container {
            width: 3.5vh;
            height: 3.5vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(184, 28, 37, 0) 100%);
            box-shadow: 0 0 3.7vh #ffffff26 inset;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fasocity-header-container-right-back-container i {
            transition: 0.2s ease-in;
        }

        .main__fasocity-header-container-right-back-container:hover {
            box-shadow: 0 0 1.7vh #ffffff80 inset;
        }

        .main__fasocity-header-container-right-back-container:hover i {
            transform: rotateY(180deg);
        }

        .main__fasocity-header-container-right-close-container {
            width: 3.5vh;
            height: 3.5vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(184, 28, 37, 0) 100%);
            box-shadow: 0 0 3.7vh #ffffff26 inset;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fasocity-header-container-right-close-container i {
            transition: 0.2s ease-in;
        }

        .main__fasocity-header-container-right-close-container:hover {
            box-shadow: 0 0 1.7vh #ffffff80 inset;
        }

        .main__fasocity-header-container-right-close-container:hover i {
            transform: rotateY(180deg);
        }

        .main__fasocity-header-bg {
            position: absolute;
            top: 2vh;
            left: 3vh;
        }

        .main__fasocity-header-bg img {
            width: 6vh;
        }

        .main__fasocity-old-header-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__fasocity-old-header-container-left {
            width: 30vh;
            height: 6.5vh;
            background: url(img/header_2.png);
            background-size: contain;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            gap: 1.5vh;
        }

        .main__fasocity-old-header-container-left-farming {
            width: 30vh;
            height: 6.5vh;
            background: url(img/header_farming.png);
            background-size: contain;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            gap: 1.5vh;
        }

        .main__fasocity-old-header-right-bar {
            display: flex;
            justify-content: center;
            position: absolute;
            top: 3.5vh;
            left: 30vh;
        }

        .main__fasocity-old-header-right-bar img {
            width: 100%;
            height: 100%;
        }

        .main__fasocity-old-header-icon-container {
            width: 4vh;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 3vh;
            margin-top: 0.35vh;
        }

        .main__fasocity-old-header-icon-container img {
            width: 7vh;
            margin-top: 0.25vh;
        }

        .main__fasocity-old-header-text-hero {
            font-size: 1.75vh;
            text-transform: uppercase;
            color: var(--color-white);
            font-weight: 800;
            font-style: italic;
        }

        .main__fasocity-old-header-text-small {
            font-size: 1.25vh;
            font-weight: 400;
            color: var(--color-opacity-white);
            font-style: italic;
            line-height: 1.3vh;
        }

        .main__fasocity-old-header-container-right {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__fasocity-old-header-container-right-focus {
            width: 2.15vh;
            height: 2.15vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid #ffc130;
            background: linear-gradient(0deg, #ffc109 0%, rgba(184, 28, 37, 0) 100%);
            box-shadow: 0 0 3.7vh #eec04b inset, 0 0.4vh 3vh #b8841c8c;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            position: relative;
            z-index: 9999;
        }
        
        .main__fasocity-old-header-container-right-focus i {
            transition: 0.2s ease-in;
        }

        .main__fasocity-old-header-container-right-focus:hover {
            opacity: 0.75;
            box-shadow: 0 0 3.7vh #eec04b inset, 0 0.4vh 5vh #ffbd17;
        }

        .main__fasocity-old-header-container-right-focus:hover i {
            transform: rotate(90deg);
        }

        .main__fasocity-old-header-container-right-close-container {
            width: 2.15vh;
            height: 2.15vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid #ff3055;
            background: linear-gradient(0deg, #ff0935 0%, rgba(184, 28, 37, 0) 100%);
            box-shadow: 0 0 3.7vh #ee4b69 inset, 0 0.4vh 3vh #b81c258c;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            position: relative;
            z-index: 9999;
        }

        .main__fasocity-old-header-container-right-close-container i {
            transition: 0.2s ease-in;
        }

        .main__fasocity-old-header-container-right-close-container:hover {
            opacity: 0.75;
            box-shadow: 0 0 3.7vh #ee4b69 inset, 0 0.4vh 5vh #ff1723;
        }

        .main__fasocity-old-header-container-right-close-container:hover i {
            transform: rotate(90deg);
        }
        .main__fasocity-old-header-text-container {
            margin-top: -0.5vh;
        }

        .main__ffa-location-container,
        .main__ffa-statistic-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 98vh;
            height: 67vh;
            background: url(img/ffa/bg.png);
            background-size: cover;
            border-radius: var(--border-radius-frame);
            padding: 4vh;
        }
        
        .main__ffa-statistic-scroll-container {
            width: 143vh;
            height: 74.6vh;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 15vh;
        }
        
        .main__ffa-location-big-container {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: hidden;
            gap: 1vh;
            height: 56vh;
            padding-right: 1vh;
            overflow-y: hidden;
            margin-top: 2vh;
        }
        
        .main__ffa-location-big-container::-webkit-scrollbar {
            height: 0.3vh;
        }
        
        .main__ffa-location-big-container::-webkit-scrollbar-track {
            height: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .main__ffa-location-big-container::-webkit-scrollbar-thumb {
            height: 0.3vh;
            background: #3971d9;
        }
        
        
        .main__ffa-statistic-kills,
        .main__ffa-statistic-deaths,
        .main__ffa-statistic-kd {
            width: 30vh;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1vh;
            border: .1vh solid #FFFF;
            border-radius: 30px;
        }
        
        .main__ffa-statistic-kills-header,
        .main__ffa-statistic-deaths-header,
        .main__ffa-statistic-kd-header {
            font-size: 1.4vh;
            text-transform: uppercase;
            color: #FFFF;
            font-weight: 500;
            text-shadow: 0vh 0vh 1.6vh #ffffff;
        }
        
        .main__ffa-statistic-kills-text,
        .main__ffa-statistic-deaths-text,
        .main__ffa-statistic-kd-text {
            font-size: 2.4vh;
            text-transform: uppercase;
            color: #fb7479;
            text-shadow: 0vh 0vh 1.7vh #fb7479;
        }
        
        .main__ffa-stats-flex-header.blue,
        .main__ffa-stats-flex-text.blue {
            color: #8d74fb;
            text-shadow: 0vh 0vh 1.7vh #8d74fb;
        }
        
        .main__ffa-stats-flex-header.orange,
        .main__ffa-stats-flex-text.orange {
            color: #fb7479;
            text-shadow: 0vh 0vh 1.7vh #fb7479;
        }
        
        .main__ffa-stats-flex-header.red,
        .main__ffa-stats-flex-text.red {
            color: #8d74fb;
            text-shadow: 0vh 0vh 1.7vh #8d74fb;
        }
        
        .main__ffa-stats-flex-header.green,
        .main__ffa-stats-flex-text.green {
            color: #fb7479;
            text-shadow: 0vh 0vh 1.7vh #fb7479;
        }
        
        .main__ffa-statistic-scroll-container {
            display: flex;
            flex-wrap: wrap;
            height: 74vh;
            flex-direction: row;
            align-content: flex-start;
            justify-content: flex-start;
            overflow-y: scroll;
            gap: .5vh;
        }
        
        .main__ffa-statistic-scroll-container::-webkit-scrollbar {
            width: 0vh;
        }
        
        .main__ffa-location-big-grid {
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: .5vh;
        }
        
        .main__ffa-location-big-item {
            width: 22vh;
            height: 52vh;
            border-radius: 0.4vh;
            overflow: hidden;
            transition: 0.2s ease-in;
            flex-shrink: 0;
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .main__ffa-location-big-item-bg {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(43, 75, 133, 0) 0%, #132756 100%);
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 2vh;
        }
        
        .main__ffa-location-button-flex {
            position: absolute;
            right: 0vh;
            margin-top: 2vh;
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        
        .main__ffa-location-button-flex-2 {
            position: absolute;
            bottom: 5.5vh;
            right: 17.4vh;
            margin-top: 2vh;
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        
        .main__ffa-location-right-information {
            position: absolute;
            top: 7.5vh;
            left: 120vh;
            width: 30vh;
        }
        
        .main__ffa-location-right-grid {
            width: 100%;
            display: grid;
            grid-template-columns: 3.5fr 1fr;
        }
        
        /* .main__ffa-location-right-icon {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        } */
        
        /* .main__ffa-location-right-icon img {
            width: 7.5vh;
            margin-top: -.9vh;
        } */
        
        .main__ffa-location-right-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #FFF;
            text-shadow: 0vh 0vh 1.7vh #FFF;
        }
        
        .main__ffa-location-right-text {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: #ffffffa2;
        }
        
        .main__ffa-statistic-scroll-item {
            width: 100%;
            display: flex;
            gap: 2vh;
            align-items: center;
            background: #5600882d;
            border: .1vh solid;
            border-image-source: linear-gradient(180deg, #74fbf65b 0%, #74fbf62c 100%);
            border-image-slice: 1;
            padding: 1vh;
            padding-top: 1.5vh;
            padding-bottom: 1.5vh;
        }
        
        .main__ffa-statistic-id {
            font-size: 2.4vh;
            text-transform: uppercase;
            color: #FFF;
            text-shadow: 0vh 0vh 1.7vh #FFF;
        }
        
        .main__ffa-statsitic-name {
            width: 50vh;
            font-size: 2.4vh;
            color: #FFF;
            text-shadow: 0vh 0vh 1.7vh #FFF;
        }
        
        .main__ffa-hud-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        .main__ffa-hud-bottom-square {
            position: absolute;
            bottom: .1vh;
            right: .1vh;
            width: .5vh;
            height: .5vh;
        }
        
        .main__ffa-hud-stats {
            position: absolute;
            bottom: 3vh;
            right: 3vh;
            width: 27vh;
        }
        
        .main__ffa-stats-header {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #FFF;
            font-weight: 500;
            border-bottom: .2vh solid #FFFFFF26;
            margin-bottom: 1vh;
        }
        
        .main__ffa-stats-flexbox {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main__ffa-stats-flex-header {
            font-size: 2.4vh;
            text-transform: uppercase;
            font-weight: 500;
            text-align: center;
        }
        
        .main__ffa-stats-flex-text {
            font-size: 1.4vh;
            text-transform: uppercase;
            font-weight: 500;
            text-align: center;
            margin-top: -.5vh;
        }
        
        .main__ffa-stats-sphere {
            position: absolute;
            bottom: -50vh;
            right: -50vh;
            width: 70vh;
            height: 70vh;
            background: #000000;
            filter: blur(40vh);
            z-index: -1;
        }
        
        .main__ffa-deathscreen-container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: radial-gradient(75% 75% at 50% 50%, rgba(18, 3, 3, 0.12) 0%, #070101 100%);
        }
        
        .main__ffa-deathscreen-top-bg {
            position: absolute;
            width: 100vw;
            top: -3vh;
        }
        
        .main__ffa-deathscreen-bottom-bg {
            position: absolute;
            width: 100vw;
            bottom: -3vh;
        }
        
        .main__ffa-deathscreen-center-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 56vh;
        }
        
        .main__ffa-deathscreen-header {
            text-align: center;
            font-size: 2.4vh;
            text-transform: uppercase;
            color: var(--clr-white);
            margin-bottom: 5vh;
        }
        
        .main__ffa-deathscreen-killed-header-container {
            width: 100%;
            position: relative;
            padding: 3vh;
            border-radius: 30px;
            box-shadow: 0 0 30px 10px #9f47b5;
            background: rgba(0, 0, 0, 0.25);
            border-image-slice: 1;
        }
        
        .main__ffa-deathscreen-killed-header-container img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
        }
        
        .main__ffa-deathscreen-killed-header-container p {
            text-align: center;
            font-size: 6.4vh;
            text-transform: uppercase;
            text-shadow: #b54747 1px 0 50px;
            color: var(--clr-white);
            font-weight: 700;
        }
        
        .main__ffa-deathscreen-respawn {
            margin-top: 5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        .main__ffa-deathscreen-respwan-header {
            text-align: center;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: var(--clr-white);
        }
        
        .main__ffa-deathscreen-respwan-time {
            text-align: center;
            font-size: 2.4vh;
            text-transform: uppercase;
            color: var(--clr-white);
        }

        .main__ffa-location-big-btns-container {
            position: absolute;
            top: 5.5vh;
            left: 26vh;
            display: flex;
            align-items: center;
            gap: 1vh;
            border-bottom: 0.1vh solid rgba(255, 255, 255, 0.05);
            width: 60vh;
        }
        
        .main__ffa-location-big-btns-item {
            width: 14vh;
            display: flex;
            align-items: center;
            gap: 1.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            border-bottom: 0.3vh solid rgba(0, 0, 0, 0);
            padding-bottom: 1vh;
            justify-content: center;
        }
        
        .main__ffa-location-big-btns-item:hover,
        .main__ffa-location-big-btns-item.active {
            border-bottom: 0.3vh solid #3971d9;
        }
        
        .main__ffa-location-big-btns-item-icon {
            font-size: 2vh;
            color: #3971d9;
        }
        
        .main__ffa-location-big-btns-item-information {
            font-size: 1.4vh;
            color: var(--color-white);
        }
        
        .main__ffa-location-big-btns-item-information p:nth-last-child(1) {
            color: #ffffff80;
            font-size: 1.2vh;
        }
        
        .main__ffa-right-stats-main-header-container {
            position: absolute;
            top: 5.6vh;
            right: 7vh;
        }
        
        .main__ffa-right-stats-main-header-container p {
            color: #fff;
            font-size: 1.3vh;
        }
        
        .main__ffa-location-big-buttom-inner-text-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main__ffa-location-big-buttom-inner-text-header p {
            font-size: 1.3vh;
            text-transform: uppercase;
            color: var(--color-white);
            font-weight: 700;
        }
        
        .main__ffa-location-big-buttom-inner-text-btn {
            padding: 0.25vh 0.5vh;
            border-radius: 0.4vh;
            border: 0.1vh solid #45ff41;
            background: linear-gradient(0deg, rgba(69, 255, 65, 0.25) 0%, rgba(69, 255, 65, 0) 100%);
            box-shadow: 0 0 3.7vh #45ff41 inset, 0 0.4vh 5.6vh #45ff4140;
        }
        
        .main__ffa-location-big-buttom-inner-text-btn p {
            font-size: 1vh;
            text-transform: uppercase;
            color: var(--color-white);
            font-weight: 400;
        }
        
        .main__ffa-location-big-buttom-inner-btn-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1vh;
            border-radius: 0.4vh;
            border: 0.1vh solid #3971d9;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0) 100%);
            box-shadow: 0 0 3.7vh #3971d9 inset, 0 0.4vh 5.6vh #3971d940;
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--color-white);
            margin-top: 0.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
        }
        
        .main__ffa-location-big-buttom-inner-btn-container:hover {
            border: 0.1vh solid #5ed939;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(84, 217, 57, 0.25) 0%, rrgba(70, 217, 57, 0) 100%);
            box-shadow: 0 0 3.7vh #4cd939 inset, 0 0.4vh 5.6vh #4cd93940;
        }
        
        .main__ffa-location-big-buttom-inner-btn-right-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5vh;
        }
        
        .main__ffa-location-big-buttom-inner-btn-right-icon,
        .main__ffa-location-big-button-inner-btn-right-text {
            padding: 0.5vh 0.75vh;
            border-radius: 0.3vh;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0) 100%);
            box-shadow: 0 0 3.7vh #3971d9 inset, 0 0.4vh 5.6vh #3971d940;
        }
        
        .main__ffa-location-big-button-inner-input-container input {
            width: 100%;
            text-align: center;
            font-family: var(--ff-header);
            font-size: 1.5vh;
            font-weight: 500;
            color: var(--color-white);
            background: rgba(0, 0, 0, 0.25);
            border: none;
            outline: none;
            margin-top: 0.5vh;
            margin-bottom: 0.5vh;
            padding: 0.5vh;
            border-radius: 0.5vh;
        }
        
        .main__ffa-location-big-button-inner-input-container input::placeholder {
            color: #ffffff8c;
        }
        
        .main__ffa-hud-container {
            font-family: var(--ff-bebas);
        }
        
        .main__ffa-hud-top-container {
            position: absolute;
            top: 22vh;
            right: 2vh;
            z-index: -15;
        }
        
        .main__ffa-hud-top-bg-container {
            position: absolute;
            top: 0;
            right: 0;
            width: 8vh;
            height: 8vh;
            border-top: 0.1vh solid rgba(255, 255, 255, 0.45);
            border-right: 0.1vh solid rgba(255, 255, 255, 0.45);
        }
        
        .main__ffa-hud-top-bg-x-1 {
            position: absolute;
            top: -1vh;
            left: -0.95vh;
            color: #ff6767;
            text-shadow: 0vh 0vh 1.7vh #ff6767;
        }
        
        .main__ffa-hud-top-bg-x-2 {
            position: absolute;
            bottom: -1vh;
            right: -0.5vh;
            color: #ff6767;
            text-shadow: 0vh 0vh 1.7vh #ff6767;
        }
        
        .main__ffa-hud-top-notify-container {
            margin-top: 1.5vh;
            margin-right: 1.5vh;
            display: flex;
            flex-direction: column;
            gap: 0.5vh;
        }
        
        .main__ffa-hud-top-notify-item {
            display: flex;
            align-items: center;
            gap: 1vh;
            padding: 0.5vh 1vh 0.5vh 1.5vh;
            background: linear-gradient(to left, #cb4f4f3f 0%, rgba(255, 255, 255, 0) 100%);
            border-width: 0.1vh;
            border-style: solid;
            border-image: linear-gradient(to left, #cb4f4f 0%, rgba(255, 255, 255, 0) 100%) 1;
            border-left: none;
        }
        
        .main__ffa-hud-top-notify-item-icon img {
            width: 1.5vh;
            margin-top: 0.25vh;
        }
        
        .main__ffa-hud-top-notify-item-name {
            font-family: var(--ff-bebas);
            font-size: 1.8vh;
            color: var(--color-white);
        }
        
        .main__ffa-hud-top-notify-item-stripe {
            width: 0.2vh;
            height: 2.5vh;
            background: #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
        }
        
        .main__ffa-hud-top-notify-item-name.murdered {
            color: #ff8282;
            text-shadow: 0vh 0vh 1.7vh #ff8282;
        }
        
        .main__ffa-hud-bottom-container {
            position: absolute;
            bottom: 2.3vh;
            right: 0vh;
            width: 45.4vh;
            height: 7.3vh;
            background: url(img/ffa/bg_1.png);
            background-size: cover;
            z-index: -15;
            transform: scale(0.8);
        }
        
        .main__ffa-hud-bottom-item-container {
            position: absolute;
            width: 10%;
            left: 3.5vh;
            bottom: 1vh;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }
        
        .main__ffa-hud-bottom-item-kills {
            width: 10.9vh;
            display: flex;
            align-items: center;
            font-size: 1.8vh;
            color: var(--color-white);
            position: relative;
            gap: 2vh;
        }
        
        .main__ffa-hud-bottom-item-kills p:first-child {
            width: 5vh;
            padding-left: 0.75vh;
        }
        
        .main__ffa-hud-bottom-item-kills p:last-child {
            width: 3vh;
            text-align: center;
            margin-left: -0.8vh;
        }
        
        .main__ffa-hud-bottom-item-deaths {
            width: 10.9vh;
            display: flex;
            align-items: center;
            font-size: 1.8vh;
            color: var(--color-white);
            gap: 0.75vh;
            margin-left: 1vh;
        }
        
        .main__ffa-hud-bottom-item-deaths p:first-child {
            width: 5vh;
            padding-left: 0.75vh;
        }
        
        .main__ffa-hud-bottom-item-deaths p:last-child {
            width: 5vh;
            text-align: center;
        }
        
        .main__ffa-hud-bottom-item-kda {
            width: 10.9vh;
            display: flex;
            align-items: center;
            font-size: 1.8vh;
            color: var(--color-white);
        }
        
        .main__ffa-hud-bottom-item-kda p:first-child {
            width: 5vh;
            padding-left: 0.75vh;
        }
        
        .main__ffa-hud-bottom-item-kda p:last-child {
            width: 5vh;
            text-align: center;
            margin-left: -1vh;
        }
        
        .main__ffa-hud-bottom-item-exp {
            width: 10.9vh;
            display: flex;
            align-items: center;
            font-size: 1.8vh;
            color: var(--color-white);
            gap: 0vh;
        }
        
        .main__ffa-hud-bottom-item-exp p:first-child {
            width: 5vh;
            padding-left: 0.75vh;
        }
        
        .main__ffa-hud-bottom-item-exp p:last-child {
            width: 5vh;
            text-align: center;
            margin-left: -0.65vh;
        }
        
        .main__ffa-hud-bottom-header {
            position: absolute;
            top: 0vh;
            left: 50%;
            transform: translate(-50%);
            font-size: 1.8vh;
            text-transform: uppercase;
            font-weight: 600;
            color: var(--color-white);
        }

        .main__dialog-npc-dialog-main {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            background-color: #17171780;
            color: #ececec;
            font-family: Montserrat, sans-serif;
        }
        
        .main__dialog-npc-dialog-main-container {
            width: 100%;
            min-height: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1vh 1vh 3vh;
            background-image: linear-gradient(to right, rgba(255, 70, 70, 0.1), rgba(23, 23, 23, 0.8) 30%, rgba(23, 23, 23, 0.8) 50%, rgba(23, 23, 23, 0.8) 70%, rgba(255, 70, 70, 0.1));
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            isolation: isolate;
        }
        
        .main__dialog-npc-dialog-main-container:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 0.2vh;
            opacity: 0.3;
            background-image: linear-gradient(to right, #ff4646, rgba(23, 23, 23, 0.8) 30%, rgba(23, 23, 23, 0.8) 50%, rgba(23, 23, 23, 0.8) 70%, #ff4646);
            box-shadow: 0 0 10px #ff4646;
        }
        
        .main__dialog-npc-dialog-main-container:after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            translate: -50% -50%;
            width: 20vh;
            height: 20vh;
            border-radius: 50%;
            background-color: #fff;
            filter: blur(100px);
            opacity: 0.2;
            z-index: -10;
        }
        
        .main__dialog-npc-dialog-main-name {
            font-size: 2vh;
        }
        
        .main__dialog-npc-dialog-main-title {
            display: flex;
            align-items: center;
            gap: 1vh;
            margin-block: 1vh;
        }
        
        .main__dialog-npc-dialog-main-title * {
            font-weight: 600;
            color: #ffffffb3;
        }
        
        .main__dialog-npc-dialog-main-title-line {
            min-width: 5rem;
            background-color: #ff4646;
            box-shadow: 0 0 10px #ff4646;
            height: 1px;
            position: relative;
        }
        
        .main__dialog-npc-dialog-main-title-line:before {
            content: "";
            position: absolute;
            top: 50%;
            translate: 0 -50%;
            width: 0.5vh;
            height: 0.5vh;
            border-radius: 50%;
            background-color: #ff4646;
            box-shadow: 0 0 10px #ff4646;
        }
        
        .main__dialog-npc-dialog-main-title-line:last-of-type:before {
            right: 0;
        }
        
        .main__dialog-npc-dialog-main-buttons {
            min-width: 40vh;
            display: grid;
            gap: 1vh;
            grid-template-columns: repeat(2, 1fr);
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
        }
        
        .main__dialog-npc-dialog-main-button {
            height: 4vh;
            background-color: #ff464633;
            border: 0.2vh solid rgba(255, 70, 70, 0.3);
            border-radius: 0.5vh;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1vh;
            padding: 1vh;
            transition: transform 0.2s ease, opacity 0.2s ease;
            will-change: transform, opacity;
        }

        .main__dialog-npc-dialog-main-button i {
            font-size: 2vh;
            color: #ff4646;
        }

        .main__dialog-npc-dialog-main-button-text {
            font-family: var(--ff-header);
            font-weight: 500;
            font-size: 1.75vh;
            margin: 0;
            display: inline-flex;
            align-items: center;
        }

        .main__dialog-npc-dialog-main-button.inactive:hover {
            cursor: unset;
        }
        
        .main__dialog-npc-dialog-main-button p {
            font-size: 1.35vh;
            font-weight: 600;
            margin: 0;
        }
        
        .main__dialog-npc-dialog-main-button svg {
            font-size: 2vh;
            fill: #ff4646;
        }
        
        .main__dialog-npc-dialog-main-button:hover {
            background-color: #ff464633;
            border-color: solid rgba(255, 70, 70, 0.3);
            cursor: pointer;
            transform: scale(1.05);
        }

        .main__dialog-npc-dialog-main-button:not(:hover) {
            opacity: 0.9;
        }
        
        .main__dialog-npc-dialog-main-button:hover ~ .main__dialog-npc-dialog-main-button {
            transform: scale(0.95);
            opacity: 1;
        }

        .main__dialog-npc-dialog-main-button > p,
        .main__dialog-npc-dialog-main-button > i {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }

        .main__dialog-npc-dialog-main-button > i {
            font-size: 2vh;
            color: white;
        }
        
        .main__dialog-npc-dialog-main-button > p {
        margin-left: 0.5vh;
        }
        
        .main__dialog-npc-dialog-main-title-line-text {
            font-size: 1.25vh;
        }
        
        .main__dialog-npc-dialog-main-typewritter {
            font-family: var(--ff-header);
            font-weight: 500;
            font-size: 1.75vh;
        }
        
        .main__dialog-npc-dialog-main-typewritter {
            margin-block: 0vh 1vh;
        }
        
        .wave-npc-dialog {
            display: inline-block;
            opacity: 0;
            transform: translateY(10px);
            animation: wave-npc-dialog 0.5s ease-in-out forwards;
        }
        
        @keyframes wave-npc-dialog {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }
            50% {
                opacity: 1;
                transform: translateY(-5px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .space {
            display: inline-block;
            width: 0.5em;
        }

        .main__multichar-container {
            position: absolute;
            top: 0vh;
            left: 0vh;
            width: 100%;
            height: 100vh;
            background: url(img/multichar/bg.png);
            background-size: cover;
            background-position: center
        }
        
        .main__multichar-content-container {
            position: absolute;
            top: 5vh;
            left: 5vh
        }
        
        .main__multichar-content-container-carousel-container {
            perspective: 1000px;
            height: 47vh;
            width: 37vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            -webkit-user-select: none;
            user-select: none;
            margin-top: 2vh
        }
        
        .main__multichar-content-container-carousel {
            position: relative;
            width: 37vh;
            height: 47vh;
            transform-style: preserve-3d
        }
        
        .main__multichar-carousel-item {
            position: absolute;
            width: 37vh;
            height: 22vh;
            border-radius: .5vh;
            padding: 1vh;
            color: #fff;
            font-size: 1.25vh;
            transition: all .3s;
            backface-visibility: hidden;
            border: .1vh solid rgba(255, 255, 255, .05);
            background-size: cover;
            background-position: center top;
            transition: .2s ease-in;
            cursor: pointer
        }
        
        .main__multichar-carousel-item:hover,
        .main__multichar-carousel-item.charactive {
            box-shadow: 0 0 2.5vh #000 inset
        }
        
        .main__multichar-carousel-item.active {
            z-index: 2
        }
        
        .main__multichar-carousel-item.preview {
            z-index: 1;
            opacity: .3
        }
        
        .main__multichar-carousel-item.behind {
            opacity: 0;
            pointer-events: none
        }
        
        .main__multichar-carousel-header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center
        }
        
        .main__multichar-carousel-header-left {
            display: flex;
            align-items: center;
            gap: 1vh
        }
        
        .main__multichar-carousel-header-left-dot {
            width: 1vh;
            height: 1vh;
            border-radius: 50%;
            background: #17c164;
            box-shadow: 0 0 1.7vh #17c164
        }
        
        .main__multichar-carousel-header-left-text p:first-child {
            color: #ffffff80
        }
        
        .main__multichar-carousel-header-left-text p:last-child {
            font-size: 1.5vh
        }
        
        .main__multichar-carousel-header-right {
            text-align: right;
            color: #ffffff80
        }
        
        .main__multichar-carousel-header-right p:last-child {
            font-size: 1.5vh;
            color: #fff
        }
        
        .main__multichar-carousel-content {
            margin-top: 1vh;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: .5vh
        }
        
        .main__multichar-carousel-content-item {
            width: 100%;
            height: 4.9vh;
            padding: 1vh;
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .05);
            box-shadow: 0 0 2.5vh #000 inset;
            display: flex;
            justify-content: space-between;
            align-items: center
        }
        
        .main__multichar-carousel-content-item-left {
            display: flex;
            align-items: center;
            gap: 1vh
        }
        
        .main__multichar-carousel-content-item-left-icon {
            width: 3.5vh;
            height: 3.5vh;
            border-radius: .25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border: .1vh solid rgba(255, 255, 255, .05);
            box-shadow: 0 0 2.5vh #000 inset
        }
        
        .main__multichar-carousel-content-item-left-icon img {
            width: 100%
        }
        
        .main__multichar-carousel-content-item-left-text p:first-child {
            color: #ffffff80
        }
        
        .main__multichar-carousel-content-item-right {
            font-family: var(--ff-arame);
            font-size: 2vh
        }
        
        .main__multichar-carousel-big-content {
            margin-top: .5vh;
            display: flex;
            flex-direction: column;
            gap: .5vh
        }
        
        .main__multichar-carousel-navigation {
            position: relative;
            margin-top: 1vh;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: .5vh;
            margin-top: -24vh;
            z-index: 1000
        }
        
        .main__multichar-carousel-navigation-btn {
            width: 100%;
            padding: 1vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            border-radius: .5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 1.5vh;
            cursor: pointer;
            transition: .2s ease-in
        }
        
        .main__multichar-carousel-navigation-btn:not(.disabled):hover,
        .main__multichar-carousel-btn-container-btn-1:not(.disabled):hover,
        .main__multichar-carousel-btn-container-btn-2:not(.disabled):hover {
            box-shadow: 0 0 3vh #380303 inset, 0 0 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent
        }
        
        .main__multichar-carousel-btn-container-btn-1.disabled,
        .main__multichar-carousel-btn-container-btn-2.disabled,
        .main__multichar-carousel-navigation-btn.disabled {
            opacity: .5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .main__multichar-carousel-btn-container {
            position: relative;
            z-index: 1000;
            margin-top: 1vh;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: .5vh
        }
        
        .main__multichar-carousel-btn-container-btn-1,
        .main__multichar-carousel-btn-container-btn-2 {
            width: 100%;
            padding: 1vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            border-radius: .5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            cursor: pointer;
            font-size: 1.5vh;
            transition: .2s ease-in
        }

        .main__pause-container {
        font-family: var(--ff-bebas);
        }

        .main__pause-menu-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 130vh;
            height: 80vh;
            background: #241b17;
            margin-top: -5vh;
            border-radius: 1.5vh;
            padding: 1vh;
            z-index: 9999;
        }

        .main__pause-menu-grid-container {
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: 0.5fr 1fr 0.5fr;
            gap: 1vh;
        }

        .main__pause-menu-grid-2-container {
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: 0.33fr 1fr;
            gap: 1vh;
        }

        .main__pause-menu-grid-left {
            width: 100%;
            height: 100%;
            background: url(img/pause/bg_1.jpg);
            background-size: 180%;
            background-position: center;
            border-radius: 0.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .main__pause-menu-grid-left#keybindsheader2 {
            width: 135%;
        }

        .main__pause-menu-grid-left#coinshopheader {
            width: 130%;
        }

        .main__pause-menu-grid-left-img-description {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .main__pause-menu-grid-left-img-description-header {
            display: flex;
            align-items: center;
            gap: 1vh;
            font-size: 4vh;
            font-weight: 700;
            color: var(--color-white);
        }

        .main__pause-menu-grid-left-img-description-header-frame {
            background: radial-gradient(277.25% 157.09% at 50% 50%, #ff0a0a 0%, rgba(255, 189, 66, 0) 100%);
            box-shadow: 0 0.4vh 2vh #ff42428c;
            font-size: 3.2vh;
            font-weight: 400;
            text-transform: uppercase;
            padding-left: 1vh;
            padding-right: 1vh;
            margin-top: -0.35vh;
            border-radius: 0.5vh;
        }

        .main__pause-menu-grid-left-img-description-subheader {
            font-size: 2vh;
            font-weight: 400;
            color: var(--color-white);
            letter-spacing: 1.2vh;
        }

        .main__pause-menu-grid-left-img-description-online {
            background: radial-gradient(rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%);
            border-radius: 0.3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1vh;
            margin-top: 1vh;
            font-size: 3.2vh;
            font-weight: 400;
            color: #ff2c2c;
            gap: 1vh;
        }

        .main__pause-menu-grid-left-img-description-online p span {
            font-size: 2vh;
            font-weight: 400;
            color: #ffffff8c;
        }

        .main__pause-menu-grid-left-img-description-online-dot {
            position: relative;
            width: 2vh;
            height: 2vh;
            border-radius: 50%;
            background: #ff2c2c2a;
            padding: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .main__pause-menu-grid-left-img-description-online-dot-inner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 1vh;
            height: 1vh;
            border-radius: 50%;
            background: #ff2c2c;
            box-shadow: 0 0 1.7vh #e2e6de;
        }

        .main__pause-menu-grid-middle-changelog-container {
            width: 100%;
            height: 50vh;
            background: url(img/pause/bg_2.png);
            background-size: cover;
            background-position: center;
            border-radius: 0.5vh;
            padding: 1vh;
        }

        .main__pause-menu-grid-middle-changelog-header-2 {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3vh;
            font-size: 3.2vh;
            font-weight: 700;
            color: #ca4e4e;
            margin-top: 1vh;
        }

        .main__pause-menu-grid-middle-changelog-header-2-img img {
            width: 2.4vh;
        }

        .main__pause-menu-grid-middle-changelog-header-2.blue {
            color: #f77373;
        }

        .main__pause-menu-grid-middle-changelog-header {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__pause-menu-grid-middle-changelog-header-icon {
            width: 5vh;
            height: 5vh;
            border-radius: 0.5vh;
            background: rgba(37, 32, 24, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #cb4f4f;
            text-shadow: 0vh 0vh 1.7vh #cb4f4f;
        }

        .main__pause-menu-grid-middle-changelog-header-info p:first-child {
            font-size: 3vh;
            text-transform: uppercase;
            font-weight: 700;
            color: #cb4f4f;
        }

        .main__pause-menu-grid-middle-changelog-header-info p:last-child {
            font-family: var(--ff-gilroy);
            font-size: 1.5vh;
            width: 40vh;
            color: #ffffff8c;
        }

        .main__pause-menu-grid-middle-changelog-scroll-container {
            width: 100%;
            margin-top: 1vh;
            height: 41.5vh;
            overflow-y: scroll;
            padding-right: 1vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
        }

        .main__pause-menu-grid-middle-changelog-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            background: #cb4f4f;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item,
        .main__pause-menu-grid-right-announce-scroll-item {
            position: relative;
            width: 100%;
            padding: 1vh;
            background: rgba(37, 24, 24, 0.5);
            display: flex;
            gap: 1vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-icon {
            width: 3.5vh;
            height: 3.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(246, 115, 115, 0.1);
            border-radius: 0.25vh;
            font-size: 1.75vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-icon i {
            margin-top: 0.25vh;
            color: #cb4f4f;
            text-shadow: 0vh 0vh 1.7vh #b17575;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-right-container {
            width: 100%;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-header p:first-child {
            font-family: var(--ff-gilroy);
            font-size: 1.6vh;
            font-weight: 500;
            color: #cb4f4f;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-header p:last-child {
            background: rgba(177, 117, 117, 0.1);
            font-family: var(--ff-gilroy);
            font-size: 1.2vh;
            color: #ffffff8c;
            border-radius: 0.25vh;
            padding: 0.5vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-line-container {
            display: flex;
            gap: 1vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            margin-top: 0.5vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-line-header {
            width: 8vh !important;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-line-header.green {
            color: #fff;
            text-shadow: 0vh 0vh 1.7vh #e4e4e4;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-line-header.orange {
            color: #cb8b4f;
            text-shadow: 0vh 0vh 1.7vh #cb8b4f;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-line-header.red {
            color: #ff2c52;
            text-shadow: 0vh 0vh 1.7vh #ff2c52;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item-line-text {
            color: #f8d6b68c;
            text-align: left;
            width: 100%;
            font-size: 1.45vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item {
            margin-top: 1vh;
        }

        .main__pause-menu-grid-middle-changelog-scroll-item:first-child {
            margin-top: 0vh;
        }

        .main__pause-menu-grid-middle-screenshot-container {
            position: relative;
            width: 100%;
            margin-top: 1vh;
            height: 26.85vh;
            border-radius: 0.5vh;
            background-size: cover;
            background-position: center bottom;
        }

        .main__pause-menu-grid-middle-screenshot-description {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 8vh;
            width: 100%;
            background: linear-gradient(180deg, rgba(20, 13, 13, 0) 0%, rgba(17, 11, 11, 0.85) 0%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 2.5vh;
            font-family: var(--ff-gilroy);
            border-radius: 0.5vh;
        }

        .main__pause-menu-grid-middle-screenshot-description p:first-child {
            color: #fff;
            font-weight: 500;
            font-size: 1.5vh;
        }

        .main__pause-menu-grid-middle-screenshot-description p:last-child {
            color: #ffffff7e;
            font-weight: 500;
            font-size: 1.2vh;
        }

        .main__pause-menu-grid-right-announce-container {
            width: 100%;
            height: 50vh;
            background: url(img/pause/bg_4.png);
            background-size: cover;
            background-position: center;
            border-radius: 0.5vh;
            padding: 1vh;
        }

        .main__pause-menu-grid-right-announce-header {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__pause-menu-grid-right-announce-header-icon {
            width: 5vh;
            height: 5vh;
            border-radius: 0.5vh;
            background: rgba(37, 29, 24, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #f67373;
            text-shadow: 0vh 0vh 1.7vh #f67373;
        }

        .main__pause-menu-grid-right-announce-header-info p:first-child {
            font-size: 3vh;
            text-transform: uppercase;
            font-weight: 700;
            color: #f67373;
        }

        .main__pause-menu-grid-right-announce-header-info p:last-child {
            font-family: var(--ff-gilroy);
            font-size: 1.5vh;
            color: #ffffff8c;
        }

        .main__pause-menu-grid-right-annouce-scroll-container {
            margin-top: 1vh;
            height: 41.5vh;
            overflow-y: scroll;
            overflow-x: hidden;
            padding-right: 1vh;
            width: 29.5vh;
        }

        .main__pause-menu-grid-right-annouce-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
        }

        .main__pause-menu-grid-right-annouce-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
        }

        .main__pause-menu-grid-right-annouce-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            background: #f67373;
        }

        .main__pause-menu-grid-right-announce-scroll-item-icon {
            width: 4vh;
            height: 3.5vh;
            background: url(img/pause/icon.jpg);
            background-size: cover;
            background-position: center;
            border-radius: 0.25vh;
        }

        .main__pause-menu-grid-right-announce-scroll-item-text {
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #f8b6b68c;
            margin-top: 0.5vh;
            width: 100%;
        }

        .main__pause-menu-grid-right-announce-scroll-item {
            margin-top: 1vh;
        }

        .main__pause-menu-grid-right-announce-scroll-item:first-child {
            margin-top: 0vh;
        }

        .main__pause-menu-grid-right-image-container {
            width: 100%;
            height: 26.85vh;
            margin-top: 1vh;
            display: grid;
            grid-template-columns: 1fr;
            gap: 1vh;
        }

        .main__pause-menu-grid-right-img-item {
            width: 100%;
            height: 100%;
            border-radius: 0.5vh;
            background-size: cover;
            background-position: center;
        }

        .main__pause-flex-container {
            position: absolute;
            bottom: 0vh;
            left: 50%;
            transform: translate(-50%);
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1vh;
        }

        .main__pause-flex-item {
            width: 11vh;
            height: 12vh;
            background: linear-gradient(to bottom, rgba(36, 23, 23, 0.85), rgb(36, 23, 23));
            border-radius: 1vh 1vh 0vh 0vh;
            padding: 1vh;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            flex-direction: column;
            gap: 1vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
        }

        .main__pause-flex-item-dash {
            width: 100%;
            height: 0.3vh;
            background: #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
            transition: 0.2s ease-in;
        }

        .main__pause-flex-item-text {
            font-size: 1.8vh;
            text-transform: uppercase;
            color: #fff;
            transition: 0.2s ease-in;
            font-weight: 300;
            font-family: 'Akrobat', sans-serif;
        }

        .main__pause-flex-item-icon {
            font-size: 2vh;
            margin-bottom: 1vh;
            color: #fff;
            transition: 0.2s ease-in-out;
        }

        .main__pause-flex-item:hover {
            border: 0.1vh solid #ff00008c;
            border-bottom: none;
            background: linear-gradient(to bottom, rgba(65, 20, 20, 0.85) 0%, rgba(81, 15, 15, 0.85) 100%);
        }

        .main__pause-flex-item:hover .main__pause-flex-item-icon,
        .main__pause-flex-item:hover .main__pause-flex-item-text {
            color: #ff1212;
        }

        .main__pause-flex-item:hover .main__pause-flex-item-dash {
            background: #ff4242;
        box-shadow: 0 0 1.7vh #ff4242;
        }

        .main__pause-flex-item.active {
            border: 0.1vh solid #ff42428c;
            border-bottom: none;
            background: linear-gradient(to bottom, rgba(65, 20, 20, 0.85) 0%, rgba(81, 15, 15, 0.85) 100%);
        }

        .main__pause-flex-item.active .main__pause-flex-item-icon,
        .main__pause-flex-item.active .main__pause-flex-item-text {
            color: #ff4242;
        }

        .main__pause-flex-item.active .main__pause-flex-item-dash {
            background: #ffffff;
            box-shadow: 0 0 1.7vh #e3e4e6;
        }

        .main__pause-menu-grid-3-right {
            width: 143%;
            height: 100%;
            background: url(img/pause/bg_8.png);
            background-size: cover;
            background-position: center;
            border-radius: 0.5vh;
            padding-top: 4vh;
            padding-left: 4vh;
        }

        .main__pause-menu-grid-3-right#fraktionspage2 {
            width: 152%;
        }

        .main__pause-menu-grid-3-right#keybindespage2 {
            width: 100%;
            margin-left: 7vh;
        }

        .main__pause-menu-grid-3-keyboard-first-line-container {
            margin-top: 1vh;
        }
        
        .main__pause-menu-grid-3-keyboard-zero-line-container {
            display: flex;
            align-items: center;
            gap: .25vh;
            margin-top: 2vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item-space {
            width: 3.5vh;
        }
        
        .main__pause-menu-grid-3-keyboard-first-line-container,
        .main__pause-menu-grid-3-keyboard-second-line-container,
        .main__pause-menu-grid-3-keyboard-third-line-container {
            display: flex;
            align-items: center;
            gap: .25vh;
        }
        
        .main__pause-menu-grid-3-keyboard-second-line-container,
        .main__pause-menu-grid-3-keyboard-third-line-container {
            margin-top: .25vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item {
            width: 5.7vh;
            height: 5.3vh;
            border-radius: .5vh;
            font-family: var(--ff-gilroy);
            font-size: 1.7vh;
            color: #ff8484;
            background: radial-gradient(99.03% 99.03% at 50% 50%, rgba(215, 79, 79, 0.55) 0%, rgba(118, 47, 47, 0) 100%);
            padding: .5vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__pause-menu-grid-3-keyboard-item:hover {
            border: .1vh solid #cb4f4f;
            background: radial-gradient(165% 163.93% at 50% 50%, rgba(216, 84, 84, 0.55) 0%, rgba(102, 43, 43, 0) 100%);
            box-shadow: 0vh .4vh 7.3713vh 0vh #cb4f4f;
            color: #cb4f4f;
        }
        
        .main__pause-menu-grid-3-keyboard-item.backspace {
            width: 10.9vh;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-content: flex-end;
        }
        
        .main__pause-menu-grid-3-keyboard-item.tab {
            width: 8.6vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.ka {
            width: 8vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.bottom {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-content: flex-end;
        }
        
        .main__pause-menu-grid-3-keyboard-item.caps {
            width: 10.7vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.enter {
            width: 11.8vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.shift {
            width: 14.3vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.shift2 {
            width: 14.1vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.bigger {
            width: 7vh;
        }
        
        .main__pause-menu-grid-3-keyboard-item.space {
            width: 37.3vh;
        }
        
        .main__pause-menu-grid-3-hotkey-information {
            padding-right: 7vh;
            margin-top: 2vh;
        }
        
        .main__pause-menu-grid-3-hotkey-wrapper {
            width: 100%;
            display: flex;
            gap: 2vh;
            border-radius: .5vh;
            background: radial-gradient(90.83% 74.5% at 50% 50%, rgba(118, 47, 47, 0.55) 0%, rgba(118, 47, 47, 0.00) 100%);
            padding: 2vh;
            border: .1vh solid #b175753b;
        }
        
        .main__pause-menu-grid-3-hotkey-left-btn {
            width: 8vh;
            height: 8vh;
            border-radius: .25vh;
            border: .1vh solid #d74f4f;
            background: radial-gradient(99.03% 99.03% at 50% 50%, #d74f4f 0%, #562121 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffb0b0;
            font-size: 4vh;
            font-weight: 700;
        }
        
        .main__pause-menu-grid-3-hotkey-right-btn {
            width: 80%;
        }
        
        .main__pause-menu-grid-3-hotkey-right-btn h1 {
            font-size: 2.75vh;
            color: var(--color-white);
            width: 80%;
        }
        
        .main__pause-menu-grid-3-hotkey-right-btn p {
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: rgba(255, 255, 255, .55);
            width: 100%;
        }
        
        .main__pause-menu-grid-3-hotkey-information-wrapper {
            position: absolute;
            width: 88vh;
            margin-top: 4vh;
            border-radius: .5vh;
            border: .1vh solid #b175753b;
            background: radial-gradient(84.36% 106.86% at 50% 50%, #2b1b1b 0%, rgba(43, 27, 27, 0) 100%);
            padding: 2vh;
            padding-bottom: 3vh;
            bottom: 2vh;
            right: 8.5vh;
        }
        
        .main__pause-menu-grid-3-hotkey-information-wrapper-header {
            position: relative;
            width: 100%;
            display: flex;
            text-align: center;
            justify-content: center;
            align-items: center;
            font-size: 2.75vh;
            color: var(--color-white);
            font-weight: 700;
        }
        
        .main__pause-menu-grid-3-hotkey-information-wrapper-header p {
            padding-left: 2vh;
            padding-right: 2vh;
            backdrop-filter: blur(2vh);
        }
        
        .main__pause-menu-grid-3-hotkey-information-wrapper-text {
            text-align: center;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: rgba(255, 255, 255, .55);
        }
        
        .main__pause-menu-grid-3-hotkey-information-wrapper-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 97%;
            height: 65%;
            margin-top: 1vh;
            border: .1vh solid rgba(255, 255, 255, .05);
        }

        .main__pause-menu-grid-2-right-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__pause-menu-grid-2-right-item-header-left {
            display: flex;
            align-items: center;
            gap: 1vh;
            color: var(--color-white);
        }

        .main__pause-menu-grid-2-right-item-header-left i {
            font-size: 2.5vh;
        }

        .main__pause-menu-grid-2-right-item-header-left h1 {
            font-size: 3.5vh;
            font-weight: 700;
        }

        .main__pause-menu-grid-2-right-item-header-left p {
            width: 23vh;
            font-family: var(--ff-gilroy);
            color: #f8ceb68c;
            font-size: 1.21vh;
        }

        .main__pause-menu-grid-4-grid-container {
            width: 100%;
            display: grid;
            grid-template-columns: 0.5fr 1fr;
            gap: 2vh;
            margin-top: 2vh;
        }

        .main__pause-menu-grid-4-grid-left-container {
            width: 100%;
            height: 100%;
        }

        .main__pause-menu-grid-4-grid-left-profile-image {
            position: relative;
            width: 100%;
            height: 30vh;
            background: url(img/pause/pb.jpg);
            background-size: cover;
            background-position: center;
            border-radius: 0.5vh;
        }

        .main__pause-menu-grid-4-grid-left-profile-image-vip {
            position: absolute;
            top: 1vh;
            right: 1vh;
            padding: 0.5vh 1vh;
            display: flex;
            align-items: center;
            gap: 0.5vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #ff5959;
            text-transform: uppercase;
            border-radius: 0.5vh;
            border: 0.1vh solid #f35454;
            background: radial-gradient(588.1% 211.38% at 50% 50%, #8e3838 0%, #d44949 100%);
        }

        .main__pause-menu-grid-4-grid-left-character-information-container {
            position: relative;
            width: 100%;
            padding: 2vh;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            margin-top: 2vh;
        }

        .main__pause-menu-grid-4-grid-left-character-information-header h2 {
            font-family: var(--ff-gilroy);
            font-size: 2vh;
            text-transform: uppercase;
            color: #f67373;
            font-weight: 600;
        }

        .main__pause-menu-grid-4-grid-left-character-information-item-container {
            margin-top: 1.5vh;
            display: flex;
            flex-direction: column;
            gap: 1vh;
        }

        .main__pause-menu-grid-4-grid-left-character-information-item {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 0.25vh;
            background: linear-gradient(90deg, rgba(177, 144, 117, 0.1) 0%, rgba(177, 149, 117, 0) 100%);
            padding: 0.75vh 0.5vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #f3e4d0;
        }

        .main__pause-menu-grid-4-grid-left-character-information-item-left {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__pause-menu-grid-4-grid-left-character-information-item-left-stripe {
            width: 0.2vh;
            height: 1.5vh;
            background: #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
        }

        .main__pause-menu-grid-4-grid-left-character-information-stripe {
            position: absolute;
            bottom: -0.25vh;
            left: 1vh;
            width: 8vh;
            height: 0.3vh;
            background: #ff2c2c;
            box-shadow: 0 0 1.7vh #ff2c2c;
        }

        .main__pause-menu-grid-4-grid-right-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2vh;
            padding-right: 4vh;
        }

        .main__pause-menu-grid-4-grid-right-item {
            width: 100%;
            height: 20vh;
            background: url(img/pause/bg_9.png);
            background-size: 105%;
            background-position: center;
            border-radius: 0.5vh;
            padding: 2vh;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item:hover {
            background-size: 120%;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-10 {
            background: url(img/pause/bg_10.png);
            background-size: 105%;
            background-position: center;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-11 {
            background: url(img/pause/bg_11.png);
            background-position: center;
            background-size: 105%;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-22 {
            background: url(img/pause/bg_22.png);
            background-position: center;
            background-size: 105%;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-12 {
            background: url(img/pause/bg_12.png);
            background-position: center;
            background-size: 105%;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-13 {
            background: url(img/pause/bg_13.png);
            background-position: center;
            background-size: 105%;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-14 {
            background: url(img/pause/bg_14.png);
            background-position: center;
            background-size: 105%;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-grid-right-item.bg-10:hover,
        .main__pause-menu-grid-4-grid-right-item.bg-11:hover,
        .main__pause-menu-grid-4-grid-right-item.bg-12:hover,
        .main__pause-menu-grid-4-grid-right-item.bg-13:hover,
        .main__pause-menu-grid-4-grid-right-item.bg-14:hover,
        .main__pause-menu-grid-4-grid-right-item.bg-22:hover {
            background-size: 120%;
        }

        .main__pause-menu-grid-4-grid-right-item-header {
            font-size: 2vh;
            font-family: var(--ff-gilroy);
            font-weight: 600;
            color: #f67373;
            text-transform: uppercase;
        }

        .main__pause-menu-grid-4-grid-right-item-info {
            position: absolute;
            padding: 0.5vh 0.75vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #cb4f4f;
            background: rgba(203, 160, 79, 0.1);
            border-radius: 0.25vh;
            text-shadow: 0vh 0vh 1.7vh #cb4f4f;
        }

        .main__pause-menu-grid-4-right-grid-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2vh;
            margin-top: 2vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left {
            display: flex;
            flex-direction: column;
            gap: 2vh;
        }

        .main__pause-menu-grid-4-right-grid-container-right {
            padding-right: 4vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item,
        .main__pause-menu-grid-4-right-grid-container-right-container {
            width: 100%;
            padding: 2vh;
            border-radius: 0.5vh;
            border: 0.1vh solid #b175753a;
            background: radial-gradient(90.83% 74.5% at 50% 50%, rgba(118, 47, 47, 0.55) 0%, rgba(118, 47, 47, 0) 100%);
        }

        .main__pause-menu-grid-4-right-grid-container-right-container {
            height: 100%;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-header,
        .main__pause-menu-grid-4-right-grid-container-right-container-header {
            font-family: var(--ff-bebas);
            font-size: 2.75vh;
            text-transform: uppercase;
            color: #ff6464;
            font-weight: 600;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report {
            width: 100%;
            padding: 1vh;
            border-radius: 0.25vh;
            background: linear-gradient(90deg, rgba(177, 117, 117, 0.1) 0%, rgba(177, 117, 117, 0) 100%);
            display: flex;
            align-items: center;
            gap: 0.5vh;
            margin-top: 1vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report-stripe {
            width: 0.2vh;
            height: 1.5vh;
            background: rgb(202, 78, 78);
            box-shadow: 0 0 1.7vh #ca4e4e;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report input {
            background: none;
            width: 100%;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #f3e2ce;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report input::placeholder {
            color: #f3e4ce;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report form textarea {
            resize: none;
            width: 35vh;
            height: 8vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #f3e0ce;
            background: none;
            outline: none;
            border: none;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report form textarea::placeholder {
            color: #f3dfce;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report-stripe-2 {
            width: 0.2vh;
            height: 8vh;
            background: rgb(202, 78, 78);
            box-shadow: 0 0 1.7vh #ca4e4e;
        }
        
        .main__pause-menu-grid-4-right-grid-container-left-item-report-btn {
            width: 100%;
            margin-top: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #ff5757;
            padding: 1vh;
            border: 0.1vh solid #ff424246;
            background: linear-gradient(180deg, rgba(65, 20, 20, 0.85) 0%, rgba(81, 15, 15, 0.85) 100%);
            cursor: pointer;
            transition: 0.2s ease-in;
            border-radius: 0.25vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-report-btn:hover {
            box-shadow: 0 0 3vh #ff4242 inset;
            color: #f3dfce;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-container {
            width: 100%;
            height: 20vh;
            overflow-y: scroll;
            padding-right: 1vh;
            margin-top: 1vh;
        }

        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-container {
            width: 100%;
            height: 55vh;
            overflow-y: scroll;
            padding-right: 1vh;
            margin-top: 1vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-container::-webkit-scrollbar,
        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-container::-webkit-scrollbar-track,
        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-container::-webkit-scrollbar-thumb,
        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            background: rgb(202, 78, 78);
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item {
            width: 100%;
            padding: 0.5vh;
            border-radius: 0.25vh;
            background: radial-gradient(99.03% 99.03% at 50% 50%, #762f2f 0%, rgba(118, 81, 47, 0) 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            margin-top: 0.5vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item:first-child {
            margin-top: 0vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item-left {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item-left-icon {
            width: 2.5vh;
            height: 2.5vh;
            border-radius: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(246, 115, 115, 0.1);
            color: #f77373;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item-left-name {
            color: #f77373;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item-right-btn {
            padding: 0.5vh 1vh;
            border-radius: 0.25vh;
            background: rgba(203, 79, 79, 0.14);
            color: #ca4e4e;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__pause-menu-grid-4-right-grid-container-left-item-scroll-item-right-btn:hover {
            color: var(--color-white);
            border: 0.1vh solid rgb(202, 78, 78);
            box-shadow: 0 0 1.7vh #ca4e4e98 inset;
        }

        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-item {
            width: 100%;
            padding: 1vh;
            border-radius: 0.25vh;
            background: radial-gradient(177.39% 145.5% at 50% 0%, rgba(133, 76, 76, 0.53) 0%, rrgba(97, 43, 43, 0)100%);
            display: flex;
            flex-direction: column;
            gap: 0.5vh;
            margin-top: 1vh;
        }

        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-item-item {
            display: flex;
            align-items: center;
            gap: 1vh;
            font-size: 1.35vh;
            font-family: var(--ff-gilroy);
            color: #f7be73;
        }

        .main__pause-menu-grid-4-right-grid-container-right-container-scroll-item:first-child {
            margin-top: 0vh;
        }

        .main__pause-menu-grid-6-grid-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1vh;
            padding-right: 4vh;
        }
        
        .main__pause-menu-grid-6-grid-item {
            position: relative;
            width: 100%;
            height: 22.5vh;
            border-radius: .5vh;
            padding: 2vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_15 {
            background: url(img/pause/bg_15.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_16 {
            background: url(img/pause/bg_16.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_17 {
            background: url(img/pause/bg_17.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_18 {
            background: url(img/pause/bg_18.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_19 {
            background: url(img/pause/bg_19.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_20 {
            background: url(img/pause/bg_20.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item.bg_23 {
            background: url(img/pause/bg_23.png);
            background-size: 110%;
            background-position: center;
        }
        
        .main__pause-menu-grid-6-grid-item-header {
            font-size: 2vh;
            font-family: var(--ff-gilroy);
            text-transform: uppercase;
        }
        
        .main__pause-menu-grid-6-grid-item:hover {
            background-size: 120%;
        }
        
        .main__pause-menu-grid-6-grid-item-header.green {
            color: #ff9a47;
            text-shadow: 0vh 0vh 1.7vh #ffaf47;
        }

        .main__pause-menu-grid-6-grid-item-header.red {
            color: #ff4752;
            text-shadow: 0vh 0vh 1.7vh #ff4752;
        }

        .main__pause-menu-grid-6-grid-item-header.orange {
            color: #ff8947;
            text-shadow: 0vh 0vh 1.7vh #ff8947;
        }

        .main__pause-menu-grid-6-grid-item-header.white,
        .main__pause-menu-grid-6-grid-item-header.yellow {
            color: #fff;
            text-shadow: 0vh 0vh 1.7vh #ffffff;
        }

        .main__pause-menu-grid-6-grid-item-header.blue {
            color: #f69c73;
            text-shadow: 0vh 0vh 1.7vh #f69c73;
        }
        
        .main__pause-menu-grid-6-grid-item-bottom {
            position: absolute;
            bottom: 2vh;
            left: 2vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: #cb4f4f;
            padding: .5vh;
            padding-left: 1vh;
            padding-right: 1vh;
            background: #cb814f1a;
            border-radius: .25vh;
        }
        
        .main__pause-menu-grid-6-container-2 {
            position: relative;
            width: 100%;
            height: 22.5vh;
            padding-right: 4vh;
        }
        
        .main__pause-menu-grid-6-container-2-item {
            position: relative;
            width: 100%;
            height: 100%;
            background: url(img/pause/bg_24.png);
            background-size: 130%;
            background-position: center bottom;
            margin-top: 1vh;
            padding: 2vh;
            border-radius: .5vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__pause-menu-grid-6-container-2-item:hover {
            background-size: 135%;
        }
        
        .main__pause-menu-grid-6-grid-item-bottom.center {
            left: 50%;
            transform: translateX(-50%);
        }

        .test1321 {
            padding-right: 4vh;
            position: relative;
        }

        .main__corleone-pause-menu-coinshop-header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1vh;
            margin-top: 2vh;
        }
        .main__corleone-pause-menu-coinshop-btn-small {
            width: 4vh;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.25vh;
            background: #f69c731a;
            color: #d74f4f;
            cursor: pointer;
            transition: 0.2s ease-in;
            border: 0.1vh solid rgba(0, 0, 0, 0);
            font-size: 1.35vh;
        }
        .main__corleone-pause-menu-coinshop-btn-small.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .main__corleone-pause-menu-coinshop-btn-small:not(.disabled):hover,
        .main__corleone-pause-menu-2-coinshop-btn-big:hover {
            border: 0.1vh solid #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
            background: radial-gradient(#d854548c, #662b2b00);
            color: var(--color-white);
        }
        .main__corleone-pause-menu-2-coinshop-btn-big {
            height: 4vh;
            width: 11.5vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.25vh;
            background: #f673731a;
            color: var(--color-white);
            cursor: pointer;
            transition: 0.2s ease-in;
            border: 0.1vh solid rgba(0, 0, 0, 0);
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
        }
        .main__corleone-pause-menu-2-coinshop-btn-big.active {
            border: 0.1vh solid #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
            background: radial-gradient(#d854548c, #662b2b00);
            color: var(--color-white);
        }
        .main__corleone-pause-menu-coinshop-scroll-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1vh;
            padding-right: 1vh;
            overflow-y: scroll;
            height: 57.5vh;
            margin-top: 2vh;
            overflow-x: hidden;
            align-content: flex-start;
            padding-bottom: 15vh;
            transform: translateZ(0) translateZ(0);
        }
        .main__corleone-pause-menu-coinshop-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
        }
        .main__corleone-pause-menu-coinshop-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
        }
        .main__corleone-pause-menu-coinshop-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            background: #cb4f4f;
        }
        .main__corleone-pause-menu-coinshop-scroll-item {
            width: 93%;
            height: 23vh;
            position: relative;
            background: url(img/pause/coin_bg.png);
            background-size: cover;
            background-position: center;
            padding: 1vh;
            border-radius: 0.25vh;
        }
        .main__corleone-pause-menu-coinshop-scroll-item-header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .main__corleone-pause-menu-coinshop-scroll-item-header-right {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh !important;
            padding: 0.5vh;
            border-radius: 0.25vh;
            background: #fb58581a !important;
            color: #fb5858 !important;
            text-shadow: 0vh 0vh 1.7vh #fb5858 !important;
        }
        .main__corleone-pause-menu-coinshop-scroll-item-header-right img {
            width: 1.3vh;
        }
        .oldPrice {
            opacity: 0.7;
            text-decoration: line-through;
            color: red;
            margin-right: -0.3vh;
            font-size: 1.13vh !important;
        }
        .newPrice {
            font-size: 1.13vh !important;
            white-space: nowrap;
        }
        .coinshop_header_left {
            font-size: 1.25vh;
            color: var(--color-white);
            font-family: var(--ff-gilroy);
        }
        #coinshop_header_left {
            font-size: 1.25vh;
            color: var(--color-white);
            font-family: var(--ff-gilroy);
        }
        .main__corleone-pause-menu-coinshop-scroll-item-img {
            width: 18.5vh;
            height: 11.5vh;
            margin-top: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
        }
        .main__corleone-pause-menu-coinshop-scroll-item-img img {
            width: 10vh;
        }
        .main__corleone-pause-menu-coinshop-scroll-item-btn {
            width: 100%;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            text-align: center;
            bottom: 1vh;
            width: 18.5vh;
            background-color: red;
            border-radius: 0.25vh;
            background: #f69c731a;
            color: var(--color-white);
            cursor: pointer;
            transition: 0.2s ease-in;
            border: 0.1vh solid rgba(0, 0, 0, 0);
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
        }
        .main__corleone-pause-menu-coinshop-scroll-item-btn:hover {
            border: 0.1vh solid #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
            background: radial-gradient(#d854548c, #2b664300);
            color: var(--color-white);
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar {
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            width: 100%;
            height: 17.5vh;
            background: linear-gradient(to bottom, rgba(53, 28, 28, 0) 0%, rgba(53, 28, 28, 0.83) 25%, rgb(36, 23, 23) 81%);
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar {
            width: 100%;
            padding-left: 4vh;
            padding-right: 4vh;
            display: grid;
            grid-template-columns: 1fr 1fr 0.1fr;
            gap: 1vh;
            display: flex;
            align-items: flex-end;
            padding-bottom: 4vh;
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-btn {
            width: 50vh;
            height: 4.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.25vh;
            background: #f673731a;
            color: var(--color-white);
            cursor: pointer;
            transition: 0.2s ease-in;
            border: 0.1vh solid rgba(0, 0, 0, 0);
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            margin-top: 1vh;
            text-transform: uppercase;
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-btn:hover {
            border: 0.1vh solid #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
            background: radial-gradient(#d854548c, #662b2b00);
            color: var(--color-white);
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-item {
            width: 100%;
            height: 4.5vh;
            background: linear-gradient(to right, #b1757521, rgba(0, 0, 0, 0));
            border-radius: 0.25vh;
            display: flex;
            align-items: center;
            gap: 1vh;
            padding-left: 1.5vh;
            font-size: 1.35vh;
            font-family: var(--ff-gilroy);
            color: var(--color-white);
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-stripe {
            width: 0.2vh;
            height: 2vh;
            background: #fb5858;
            box-shadow: 0 0 1.7vh #fb5858;
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-coin img {
            width: 2.5vh;
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-item input {
            width: 90%;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            color: var(--color-white);
            outline: none;
            border: none;
            background: none;
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-item input::placeholder {
            color: var(--color-white);
            border: none;
        }
        .main__corleone-pause-menu-coinshop-bottom-navbar-stripe.green {
            background: #cb4f4f;
            box-shadow: 0 0 1.7vh #cb4f4f;
        }
        .main__corleone-pause-menu-coinshop-item-name {
            width: 100%;
            height: 3.1vh;
            border-radius: 0.25vh;
            background: #f673731a;
            margin-top: 2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            font-family: var(--ff-gilroy);
            font-size: 1.5vh;
            color: #cb4f4f;
            text-shadow: 0vh 0vh 1.7vh #cb4f4f;
        }
        .main__corleone-pause-menu-coinshop-item-close {
            position: absolute;
            right: 1vh;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2vh;
            color: #ff5a5a;
            text-shadow: 0vh 0vh 1.7vh #ff5a5a;
            cursor: pointer;
            transition: 0.2s ease-in;
        }
        .main__corleone-pause-menu-coinshop-item-close:hover {
            opacity: 0.5;
        }
        .main__corleone-pause-menu-coinshop-grid-container {
            width: 100%;
            height: 54vh;
            display: grid;
            grid-template-columns: 0.05fr 1fr 0.05fr;
            gap: 1vh;
            margin-top: 1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-container-left,
        .main__corleone-pause-menu-coinshop-grid-container-right {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.2s ease-in;
            background: #f673731a;
            font-size: 1.35vh;
            color: #d74f4f;
            border-radius: 0.25vh;
        }
        .main__corleone-pause-menu-coinshop-grid-container-left.disabled,
        .main__corleone-pause-menu-coinshop-grid-container-right.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }
        .main__corleone-pause-menu-coinshop-grid-container-left:not(.disabled):hover,
        .main__corleone-pause-menu-coinshop-grid-container-right:not(.disabled):hover {
            background: #f6737336;
            box-shadow: 0 0 1.7vh #f6737336;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-image-preview-container {
            position: relative;
            width: 100%;
            background: url(img/pause/preview_bg.png);
            background-size: cover;
            height: 24.2vh;
            border-radius: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-image-preview-info-container {
            position: absolute;
            top: 0.5vh;
            right: 0.5vh;
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-image-preview-info-eye {
            width: 2.5vh;
            height: 2.5vh;
            background: #f6737340;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.35vh;
            color: #ff8484;
            cursor: pointer;
            transition: 0.2s ease-in;
            border-radius: 0.25vh;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-image-preview-info-eye:hover {
            text-shadow: 0vh 0vh 1.7vh #ff8484;
            background: #f673737c;
            box-shadow: 0 0 1.7vh #f6737340;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-image-preview-info-eye i {
            margin-top: 0.1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-car-preview {
            width: 25vh;
            height: 20vh;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-car-preview img,
        .main__corleone-pause-menu-coinshop-grid-container-middle-car-preview-blur img {
            width: 80%;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-car-preview-blur {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 25vh;
            height: 20vh;
            display: flex;
            justify-content: center;
            align-items: center;
            filter: blur(3vh);
            z-index: 1;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-container {
            width: 100%;
            margin-top: 1vh;
            padding: 1vh;
            background: #cb4f4f1a;
            border-radius: 0.25vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            color: #cb4f4f;
            text-shadow: 0vh 0vh 1.7vh #cb4f4f;
            font-family: var(--ff-gilroy);
            background: radial-gradient(#cb4f4f40, #cb4f4f00);
            height: 3.1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1vh;
            margin-top: 2vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-container-2 {
            width: 100%;
            display: flex;
            justify-content: center;
            padding-left: 2vh;
            align-items: center;
            margin-top: 2vh;
            gap: 1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item {
            display: flex;
            align-items: center;
            gap: 0.5vh;
            width: 100%;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-icon {
            width: 4vh;
            height: 4vh;
            background: radial-gradient(#dd53538c, #782f2f00);
            font-size: 1.75vh;
            color: #cb4f4f;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-information {
            display: flex;
            align-items: center;
            gap: 1vh;
            height: 4vh;
            background: linear-gradient(to right, #dd535340, #782f2f00);
            border-radius: 0.25vh;
            font-size: 1.35vh;
            font-family: var(--ff-gilroy);
            padding-left: 0.65vh;
            color: #d85454;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-information-left p:last-child {
            font-size: 1.2vh;
            font-weight: 500;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-information-right {
            display: flex;
            align-items: center;
            gap: 0.3vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-information-right-stripe {
            width: 0.3vh;
            height: 2vh;
            background: #e3e3e340;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-specification-item-information-right-stripe.active {
            background: #d85454;
            box-shadow: 0 0 1.7vh #d85454;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-container {
            width: 100%;
            padding: 1vh;
            margin-top: 1vh;
            background: #f673731a;
            border-radius: 0.25vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            font-family: var(--ff-gilroy);
            height: 3.1vh;
            background: radial-gradient(#f673731a, #f6737300);
            color: #cb4f4f;
            text-shadow: 0vh 0vh 1.7vh #cb4f4f;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-description-header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            font-family: var(--ff-gilroy);
            height: 3.1vh;
            background: radial-gradient(#cb4f4f40, #f6737300);
            color: #cb4f4f;
            text-shadow: 0vh 0vh 1.7vh #cb4f4f;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item {
            width: 4.1vh;
            height: 4.1vh;
            background: #fff;
            border-radius: 0.25vh;
            cursor: pointer;
            transition: 0.2s ease-in-out;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item:hover,
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.active {
            outline: 0.3vh solid #fff;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.red {
            background: #ff5a5a;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.orange {
            background: #ffb35a;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.yellow {
            background: #fff85a;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.green-1 {
            background: #8fff5a;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.green-2 {
            background: #ff9c5a;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.green-3 {
            background: #5affc4;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.blue-1 {
            background: #5ae1ff;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.blue-2 {
            background: #5a6bff;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.blue-3 {
            background: #715aff;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.purple-1 {
            background: #995aff;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.purple-2 {
            background: #e85aff;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.purple-3 {
            background: #ff5abd;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.red-2 {
            background: #355d9a;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.turquise-1 {
            background: #348a9d;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-color-item.black {
            background: #000;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-btn-container {
            margin-top: 1vh;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-btn-1,
        .main__corleone-pause-coinshop-popup-btn-1 {
            width: 100%;
            height: 5.5vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            background: radial-gradient(#d854548c, #662b2b00);
            border-radius: 0.25vh;
            border: 0.1vh solid #cb4f4f91;
            color: var(--color-white);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.2s ease-in;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-btn-1:hover,
        .main__corleone-pause-coinshop-popup-btn-1:hover {
            box-shadow: 0 0 3vh #cb4f4f86;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-btn-2,
        .main__corleone-pause-coinshop-popup-btn-2 {
            width: 100%;
            height: 5.5vh;
            font-family: var(--ff-gilroy);
            font-size: 1.35vh;
            background: radial-gradient(#cb4f4f8c, #6624248c);
            border-radius: 0.25vh;
            border: 0.1vh solid #d849499a;
            color: var(--color-white);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.2s ease-in;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-btn-2:hover,
        .main__corleone-pause-coinshop-popup-btn-2:hover {
            box-shadow: 0 0 3vh #d849499a;
        }
        .main__corleone-pause-menu-coinshop-grid-container-middle-2-container {
            width: 100%;
            display: grid;
            grid-template-columns: 0.6fr 0.4fr;
            gap: 1vh;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-description-container {
            width: 100%;
            margin-top: 1vh;
            padding: 1vh;
            border-radius: 0.25vh;
            background: #f673731a;
            font-size: 1.35vh;
            font-family: var(--ff-gilroy);
            text-align: center;
        }
        .main__corleone-pause-menu-coinshop-grid-middle-description-info {
            margin-top: 1vh;
            color: #ffffff85;
            font-size: 1.4vh;
            line-height: 2vh;
        }
        .main__corleone-pause-menu-coinshop-popup-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            width: 36vh;
            padding: 2vh;
            border-radius: 1vh;
            background: url(img/pause/popup_bg.png);
            background-size: cover;
            background-position: center;
        }
        .main__corleone-pause-menu-coinshop-popup-content {
            width: 100%;
            padding: 2vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            text-align: center;
            border-radius: 0.5vh;
        }
        .main__corleone-pause-menu-coinshop-popup-icon {
            position: absolute;
            top: 0.65vh;
            left: 50%;
            transform: translate(-50%);
            width: 7vh;
            height: 3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2vh;
            color: #ffffff8c;
            -webkit-backdrop-filter: blur(1vh);
            backdrop-filter: blur(1vh);
        }
        .main__corleone-pause-menu-coinshop-popup-content h2 {
            font-size: 2vh;
            color: var(--color-white);
        }
        .coinshop-popup-p-1 {
            font-size: 1.35vh;
            color: #ffffff8c;
            font-family: var(--ff-gilroy);
            margin-top: 0.5vh;
            font-weight: 500;
        }
        .coinshop-popup-p-2 {
            font-size: 1.5vh;
            color: #cb4f4f;
            font-family: var(--ff-gilroy);
            margin-top: 1vh;
            font-weight: 500;
        }
        .coinshop-popup-p-3 {
            font-size: 1.35vh;
            color: #ff2c2c;
            font-family: var(--ff-gilroy);
            margin-top: 1vh;
            font-weight: 500;
            text-shadow: 0vh 0vh 1.7vh #ff2c2c;
        }
        .main__corleone-pause-coishop-popup-btn-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
            margin-top: 2vh;
        }
        .main__corleone-pause-menu-coinshop-category-container {
            display: flex;
            align-content: flex-start;
            flex-direction: row;
            gap: 1.5vh;
            width: 77vh;
            overflow: hidden;
            transform: translateZ(0) translateZ(0);
        }

        .main__corleone-pause-menu-grid-3-right {
            width: 100%;
            height: 100%;
            background: url(img/pause/bg_8.png);
            background-size: cover;
            background-position: center;
            border-radius: 0.5vh;
            padding-top: 4vh;
            padding-left: 4vh;
        }

        .main__corleone-pause-menu-grid-3-right#coinshoppage {
            margin-left: 6vh;
        }

        .main__corleone-pause-menu-grid-2-right-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-left {
            display: flex;
            align-items: center;
            gap: 1vh;
            color: var(--color-white);
        }

        .main__corleone-pause-menu-grid-2-right-item-header-left i {
            font-size: 2.5vh;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-left h1 {
            font-size: 3.5vh;
            font-weight: 700;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-left p {
            width: 23vh;
            font-family: var(--ff-gilroy);
            color: #f8ceb68c;
            font-size: 1.21vh;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-left.quest p {
            width: 35vh;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-right {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-right.lifetime {
            margin-right: 0.7vh;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-right-btn {
            width: 3.5vh;
            height: 3.5vh;
            border-radius: 0.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.2s ease-in;
            background: radial-gradient(93.94% 93.94% at 50% 50%, rgba(255, 255, 255, 0.19) 0%, rgba(255, 255, 255, 0) 100%);
            font-size: 1.2vh;
            color: #ffffff8c;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-right-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .main__corleone-pause-menu-grid-2-right-item-header-right-btn:not(.disabled):hover {
            box-shadow: 0 0 1.7vh #ffffff59 inset;
        }

        .main__workstation-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 75vh;
            padding: 3vh;
            border-radius: 1vh;
            background: url(img/workstation/bg.png);
            background-size: cover;
            background-position: center;
        }
        
        .main__workstation-grid-container {
            margin-top: 2vh;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1vh;
        }
        
        .main__workstation-grid-item {
            position: relative;
            width: 100%;
            height: 28.5vh;
            transition: .2s ease-in;
            background: url(img/workstation/item_1.png);
            background-size: cover;
        }
        
        .main__workstation-grid-item-img {
            /* height: 15vh;
            margin-top: 4vh;
            padding-left: 4vh;
            padding-right: 3vh; */
            height: 16vh;
            width: 16vh;
            position: absolute;
            transform: translate(-50%, -50%);
            top: 50%;
            left: 50%;
            margin-top: -3vh;
        }
        
        .main__workstation-grid-item-img img {
            width: 100%;
            height: 100%;
        }
        
        .main__workstation-grid-item.item_2 {
            background: url(img/workstation/item_2.png);
            background-size: cover;
            height: 28.5vh;
        }
        
        .main__workstation-grid-item.item_3 {
            background: url(img/workstation/item_3.png);
            background-size: 100%;
            background-position: center;
            height: 28.5vh;
            transition: .2s ease-in;
        }
        
        .main__workstation-grid-item-description {
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            width: 100%;
            padding: 1.5vh;
            text-align: center;
            font-size: 1.2vh;
            color: var(--color-white);
            background: rgba(0, 0, 0, .25);
        }
        
        .main__workstation-grid-item-description-2 {
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            width: 100%;
            padding: 1.5vh;
            text-align: center;
            font-size: 1.2vh;
            color: var(--color-white);
            background: rgba(0, 0, 0, .25);
        }
        
        .main__workstation-grid-item-progress-container {
            width: 100%;
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            padding: 2vh;
        }
        
        .main__workstation-grid-item-progress-item-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: .5vh;
        }
        
        .main__workstation-grid-item-progress-item {
            width: 100%;
            height: 2vh;
            background: rgba(57, 113, 217, 0.15);
            border-radius: .2vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .4vh;
        }
        
        .main__workstation-grid-item-progress-item-active {
            width: 100%;
            height: 100%;
            border-radius: .2vh;
            background: #3971D9;
            box-shadow: 0vh 0vh 1.1vh 0vh #3971D9;
        }
        
        .main__workstation-bottom-container {
            position: absolute;
            bottom: 19vh;
            left: 50%;
            transform: translateX(-50%);
            width: 73vh;
            padding: 2vh;
            border-radius: var(--border-radius-frame);
            background: url(img/workstation/bg.png);
            background-size: cover;
            overflow: hidden;
        }
        
        .main__workstation-bottom-flex-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main__workstation-bottom-flex-container-left {
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        
        .main__workstation-bottom-flex-container-left-img {
            width: 4vh;
            height: 4vh;
            background: rgba(0, 0, 0, .25);
            border-radius: .4vh;
        }
        
        .main__workstation-bottom-flex-container-left-img img {
            width: 100%;
            height: 100%;
            border-radius: .4vh;
            padding: .25vh;
        }
        
        .main__workstation-bottom-flex-container-left-text {
            font-size: 1.5vh;
            color: var(--color-white);
        }
        
        .main__workstation-bottom-flex-container-left-text p:nth-last-child(1) {
            font-size: 1.2vh;
            color: rgba(255, 255, 255, .5);
        }
        
        .main__workstation-bottom-flex-container-progress-top input {
            width: 20vh;
            height: .5vh;
            -webkit-appearance: none;
            appearance: none;
            border-radius: 5vh;
            height: .5vh;
            background: radial-gradient(278.27% 67.86% at 50% 50%, rgba(212, 212, 212, 0.25) 0%, rgba(200, 200, 200, 0.00) 100%);
            margin-top: -.5vh;
        }
        
        .main__workstation-bottom-flex-container-progress-top input::-webkit-slider-thumb {
            appearance: none;
            -webkit-appearance: none;
            width: 1vh;
            height: 1vh;
            border-radius: 5vh;
            background: radial-gradient(278.27% 67.86% at 50% 50%, #3971D9 0%, rgba(57, 113, 217, 0.00) 100%);
            cursor: pointer;
        }
        
        .main__workstation-bottom-flex-container-progress-bottom {
            width: 20vh;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 1.2vh;
            color: var(--color-white);
        }
        
        .main__workstation-bottom-flex-container-progress-bottom input {
            width: 4vh;
            background: none;
            outline: none;
            border: none;
            font-family: var(--ff-inter);
            text-align: center;
            color: var(--clr-white);
            font-size: 1.2vh;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .main__workstation-bottom-flex-container-progess-btn-container {
            display: flex;
            align-items: center;
            gap: .5vh;
        }
        
        .main__workstation-bottom-flex-container-progess-btn {
            padding: 1vh;
            padding-left: 2vh;
            padding-right: 2vh;
            text-align: center;
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--color-white);
            border-radius: .2vh;
            border: .1vh solid #3971D9;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(57, 113, 217, 0.25) 0%, rgba(57, 113, 217, 0.00) 100%);
            box-shadow: 0vh 0vh 3.7vh 0vh #3971D9 inset, 0vh .4vh 5.6vh 0vh rgba(57, 113, 217, 0.25);
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__workstation-bottom-flex-container-progess-btn.inactive {
            opacity: .5;
            cursor: not-allowed;
        }
        
        .main__workstation-bottom-flex-container-progess-btn:not(.inactive):hover {
            box-shadow: 0vh 0vh 1.7vh #3971D9 inset;
        }
        
        .main__workstation-bottom-flex-container-progess-btn.exit {
            border-radius: .2vh;
            border: .1vh solid #D93939;
            background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(217, 57, 57, 0.25) 0%, rgba(217, 57, 57, 0.00) 100%);
            box-shadow: 0vh 0vh 3.7vh 0vh #D93939 inset, 0vh .4vh 5.6vh 0vh rgba(217, 57, 57, 0.25);
        }
        
        .main__workstation-bottom-flex-container-progess-btn.exit:not(.inactive):hover {
            box-shadow: 0vh 0vh 1.7vh #D93939 inset;
        }

        .main__clothing-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: url(img/clothing/bg.png);
            background-size: cover;
            background-position: center top left;
            z-index: -1;
        }
        
        .main__clothing-container {
            width: 32.5vh;
            height: 100vh;
            padding: 2vh;
        }
        
        .main__clothing-grid-item-header {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: var(--ff-bebas);
            padding-bottom: 1vh;
        }
        
        .main__clothing-grid-item-header-left {
            text-transform: uppercase;
            color: rgba(255, 255, 255, .5);
            font-size: 3vh;
            font-weight: 300;
        }
        
        .main__clothing-grid-item-header-left p span {
            font-weight: 500;
            color: #fff;
        }
        
        .main__clothing-grid-item-header-right-btn {
            width: 3.25vh;
            height: 3.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(to top, #FF0935, #be3b42);
            color: #fff;
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .05);
            cursor: pointer;
            transition: .2s ease-in;
            font-size: 1.5vh;
        }
        
        .main__clothing-grid-item-header-right-btn i {
            transition: .2s ease-in;
        }
        
        .main__clothing-grid-item-header-right-btn:hover {
            box-shadow: 0vh 0vh 1.7vh #be3b42;
        }
        
        .main__clothing-grid-item-header-right-btn:hover i {
            transform: rotate(90deg);
        }
        
        .main__clothing-grid-item-header-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: .1vh;
            background: linear-gradient(to right, rgba(255, 255, 255, .25), rgba(255, 255, 255, 0));
        }
        
        .main__clothing-grid-item-header-border-highlight {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 17.5vh;
            height: .1vh;
            background: #fd0303;
            box-shadow: 0vh 0vh 1.7vh #fd0303;
        }
        
        .main__clothing-category-btn-container {
            width: 100%;
            margin-top: 1.5vh;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1vh;
        }
        
        .main__clothing-category-btn {
            position: relative;
            padding: 1vh;
            border-radius: .3vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            font-size: 1.25vh;
            color: #fff;
            text-align: center;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-category-btn:hover,
        .main__clothing-category-btn.active {
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__clothing-category-search {
            margin-top: 1vh;
            width: 100%;
            padding: 1vh;
            display: flex;
            align-items: center;
            gap: 1.5vh;
            border-radius: .3vh;
            border: .1vh solid rgba(255, 255, 255, .05);
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            font-size: 1.5vh;
            color: #fff;
        }
        
        .main__clothing-category-search input {
            font-family: var(--ff-body);
            color: #fff;
            width: 100%;
            padding-bottom: .25vh;
            border-bottom: .1vh solid rgba(255, 255, 255, .05);
            outline: none;
            border: none;
            background: none;
        }
        
        .main__clothing-category-search input::placeholder {
            color: rgba(255, 255, 255, .5);
        }
        
        .main__clothing-scroll-container {
            width: 100%;
            height: 51vh;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
            padding-right: 1vh;
            margin-top: 1vh;
            overflow-y: scroll;
        }
        
        .main__clothing-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
            border-radius: 5vh;
        }

        .main__clothing-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            border-radius: 5vh;
            background: rgba(255, 255, 255, 0.05);
        }

        .main__clothing-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            border-radius: 5vh;
            background: #c11717;
        }
        
        .main__clothing-scroll-item {
            width: 100%;
            height: 12vh;
            /* background: url(img/clothing/item_bg.svg);    background-size: 110%;    background-position: center; */
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .05);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-scroll-item:hover {
            background-size: 150%;
            background-position: center;
        }
        
        .main__clothing-scroll-item-name {
            font-family: var(--ff-bebas);
            font-size: 2vh;
            color: #fff;
            margin-top: 1vh;
        }
        
        .main__clothing-scroll-item-img {
            width: 7.5vh;
            height: 7vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .main__clothing-scroll-item-img img {
            width: 100%;
        }
        
        .main__clothing-input-item-wrapper {
            margin-top: 1vh;
            width: 100%;
            padding: .5vh;
            border-radius: .5vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .05);
        }
        
        .main__clothing-input-item {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main__clothing-input-item-btn {
            width: 3vh;
            height: 3vh;
            border-radius: .25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            cursor: pointer;
            transition: .2s ease-in;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            color: #fff;
        }
        
        .main__clothing-input-item-btn:hover {
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__clothing-input-item-input input {
            font-family: var(--ff-body);
            font-size: 1.5vh;
            color: #ffffff;
            width: 100%;
            text-align: center;
            outline: none;
            border: none;
            background: none;
            font-weight: 200;
            width: 20vh;
            height: 3vh;
            padding: 0.5vh;
            border-bottom: 0.1vh solid rgba(255, 255, 255, 0.05);
        }
        
        .main__clothing-input-item-wrapper-header {
            color: #fff;
            padding-bottom: 1vh;
        }
        
        .main__clothing-input-item-wrapper-header p {
            font-family: var(--ff-arame);
            font-size: 1.5vh;
            font-weight: 300;
            color: rgba(255, 255, 255, .5);
        }
        
        .main__clothing-input-item-wrapper-header p span {
            font-weight: 500;
            font-family: var(--ff-arame);
            font-size: 1.5vh;
            color: #fff;
        }
        
        .main__clothing-input-item-wrapper-header-stripe {
            position: relative;
            width: 100%;
            margin-top: .1vh;
            height: .1vh;
            background: linear-gradient(to right, rgba(255, 255, 255, .1), rgba(255, 255, 255, 0));
        }
        
        .main__clothing-input-item-wrapper-header-stripe-inner {
            position: absolute;
            left: 0;
            top: 0;
            width: 10vh;
            height: .1vh;
            background: #fd0303;
            box-shadow: 0vh 0vh 1.7vh #fd0303;
        }
        
        .main__clothing-price-container {
            margin-top: 1vh;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr .5fr;
            gap: 1vh;
        }
        
        .main__clothing-price-container-left {
            width: 100%;
            height: 5vh;
            padding: .5vh;
            padding-left: 1vh;
            padding-right: 1vh;
            border-radius: .5vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
            font-size: 1.5vh;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .main__clothing-price-container-left p:first-child {
            color: #fff;
        }
        
        .main__clothing-price-container-left p:last-child {
            color: #fb6767;
            text-shadow: 0vh 0vh 1.7vh #fb6767;
        }
        
        .main__clothing-price-container-right-btn-1 {
            position: relative;
            width: 5vh;
            height: 5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: .5vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-price-container-right-btn-1:hover {
            box-shadow: 0vh 0vh 1.7vh #ff5e5e inset;
        }
        
        .main__clothing-price-container-right-btn-1 img {
            width: 70%;
        }
        
        .main__clothing-price-container-right-btn-2 {
            position: relative;
            width: 5vh;
            height: 5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: .5vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-price-container-right-btn-3 {
            position: relative;
            width: 5vh;
            height: 5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: .5vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-price-container-right-btn-2:hover {
            box-shadow: 0vh 0vh 1.7vh #FF275B inset;
        }
        
        .main__clothing-price-container-right-btn-2 img,
        .main__clothing-price-container-right-btn-3 img {
            width: 70%;
        }
        
        .main__clothing-price-container-right {
            display: flex;
            align-items: center;
            gap: 1vh;
        }
        
        .main__clothing-price-container-right-btn-3:hover {
            box-shadow: 0vh 0vh 1.7vh #fb6767 inset;
        }
        
        .main__clothing-price-container-right-btn-1-stripe {
            position: absolute;
            bottom: -.1vh;
            left: 50%;
            transform: translateX(-50%);
            height: .2vh;
            width: 3vh;
            background: #ff5e5e;
            box-shadow: 0vh 0vh 1.7vh #ff5e5e;
        }
        
        .main__clothing-price-container-right-btn-4-stripe {
            position: absolute;
            bottom: -.1vh;
            left: 50%;
            transform: translateX(-50%);
            height: .2vh;
            width: 3vh;
            background: #ff0000;
            box-shadow: 0vh 0vh 1.7vh #ff0000;
        }
        
        .main__clothing-price-container-right-btn-2-stripe {
            position: absolute;
            bottom: -.1vh;
            left: 50%;
            transform: translateX(-50%);
            height: .2vh;
            width: 3vh;
            background: #FF275B;
            box-shadow: 0vh 0vh 1.7vh #FF275B;
        }
        
        .main__clothing-price-container-right-btn-3-stripe {
            position: absolute;
            bottom: -.1vh;
            left: 50%;
            transform: translateX(-50%);
            height: .2vh;
            width: 3vh;
            background: #fb6767;
            box-shadow: 0vh 0vh 1.7vh #fb6767;
        }
        
        .main__clothing-outfit-container,
        .main__clothing-safe-outfit-container {
            width: 100%;
            height: 80vh;
            padding-right: 1vh;
            overflow-y: scroll;
            margin-top: 1vh;
        }
        
        .main__clothing-outfit-container::-webkit-scrollbar,
        .main__clothing-safe-outfit-container::-webkit-scrollbar {
            width: .3vh;
            border-radius: 5vh;
        }
        
        .main__clothing-outfit-container::-webkit-scrollbar-track,
        .main__clothing-safe-outfit-container::-webkit-scrollbar-track {
            width: .3vh;
            border-radius: 5vh;
            background: rgba(255, 255, 255, .05);
        }
        
        .main__clothing-outfit-container::-webkit-scrollbar-thumb,
        .main__clothing-safe-outfit-container::-webkit-scrollbar-thumb {
            width: .3vh;
            border-radius: 5vh;
            background: #fd0303;
        }
        
        .main__clothing-outfit-item {
            width: 100%;
            display: grid;
            grid-template-columns: 1fr .025fr;
            gap: .5vh;
            margin-top: 1vh;
            transition: .2s ease-in;
        }
        
        .main__clothing-outfit-item:first-child {
            margin-top: 0vh;
        }
        
        .main__clothing-outfit-item-left {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: .5vh;
            width: 100%;
            padding: .5vh;
            border-radius: .5vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
        }
        
        .main__corleone-outfit-item-right {
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
            border-radius: .5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .5vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__corleone-outfit-item-right-2 {
            border: .1vh solid #FF3055;
            background: linear-gradient(0deg, #FF0935 0%, rgba(184, 28, 37, 0.00) 100%);
            box-shadow: 0vh 0vh 3.7vh 0vh #EE4B69 inset, 0vh .4vh 3vh 0vh rgba(184, 28, 37, 0.55);
            padding: .5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            color: #fff;
            border-radius: .5vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__corleone-outfit-item-right-2:hover {
            box-shadow: 0vh 0vh 1.7vh #FF275B inset;
        }
        
        .main__corleone-outfit-item-right i {
            font-size: 1.5vh;
            color: rgba(255, 255, 255, .5);
            transition: .2s ease-in;
        }
        
        .main__corleone-outfit-item-right:hover {
            box-shadow: 0vh 0vh 1.7vh #fd0303 inset;
            color: #fff !important;
        }
        
        .main__corleone-outfit-item-right:hover i {
            color: #fff;
        }
        
        .main__clothing-outfit-item-icon {
            width: 4vh;
            height: 4vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .main__clothing-outfit-item-icon img {
            width: 100%;
        }
        
        .main__clothing-outfit-item-name {
            width: 12.5vh;
            font-size: 1.5vh;
            color: rgba(255, 255, 255, .5);
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-outfit-item-name:hover {
            opacity: .5;
        }
        
        .main__clothing-outfit-item-name-2 {
            width: 19vh;
            font-size: 1.5vh;
            color: rgba(255, 255, 255, .5);
        }
        
        .main__clothing-outfit-item-name P:first-child {
            font-size: 1vh;
        }
        
        .outfit__name {
            font-size: 1vh;
        }
        
        .main__clothing-outfit-item-name p:last-child {
            font-size: 1.5vh;
            color: #fd0303;
            text-shadow: 0vh 0vh 1.7vh #fd0303;
        }
        
        .outfit__name-2 {
            font-size: 1.5vh;
            color: #fd0303;
            text-shadow: 0vh 0vh 1.7vh #fd0303;
        }
        
        .main__clothing-outfit-btn-container {
            display: flex;
            align-items: center;
            gap: .5vh;
        }
        
        .main__clothing-outfit-btn-1 {
            width: 2.5vh;
            height: 2.5vh;
            border-radius: .25vh;
            cursor: pointer;
            transition: .2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.25vh;
            border: .1vh solid #fb6767;
            background: linear-gradient(0deg, #684329 0%, rgba(184, 28, 37, 0.00) 100%);
            box-shadow: 0vh 0vh 3.7vh 0vh #fb6767 inset, 0vh .4vh 3vh 0vh rgba(184, 98, 28, 0.55);
            color: #fff;
        }
        
        .main__clothing-outfit-btn-1:hover {
            box-shadow: 0vh 0vh 1.7vh #fb6767 inset;
        }
        
        .main__clothing-outfit-btn-2 {
            width: 2.5vh;
            height: 2.5vh;
            border-radius: .25vh;
            cursor: pointer;
            transition: .2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            border: .1vh solid #FF3055;
            background: linear-gradient(0deg, #FF0935 0%, rgba(184, 28, 37, 0.00) 100%);
            box-shadow: 0vh 0vh 3.7vh 0vh #EE4B69 inset, 0vh .4vh 3vh 0vh rgba(184, 28, 37, 0.55);
            color: #fff;
        }
        
        .main__clothing-outfit-btn-1 i {
            margin-top: .25vh;
        }
        
        .main__clothing-outfit-btn-2:hover {
            box-shadow: 0vh 0vh 1.7vh #FF3055 inset;
        }
        
        .main__clothing-grid-item-right-popup {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            background: url(img/shop/bg.png);
            background-size: cover;
            background-position: center;
            width: 30vh;
            padding: 2vh;
            border-radius: 1vh;
        }
        
        .main__clothing-grid-item-right-popup-header {
            font-family: var(--ff-arame);
            font-size: 2vh;
            font-weight: 500;
            color: #fff;
            text-align: center;
            width: 100%;
        }
        
        .main__clothing-grid-item-right-popup-header p:first-child {
            font-size: 1.5vh;
            color: rgba(255, 255, 255, .5);
        }
        
        .main__clothing-grid-item-right-popup-text {
            margin-top: .5vh;
            color: rgba(255, 255, 255, .5);
            font-size: 1.5vh;
            width: 100%;
            text-align: center;
        }
        
        .main__clothing-grid-item-right-popup-text p span {
            color: #fd0303;
            text-shadow: 0vh 0vh 1.7vh #fd0303;
        }
        
        .main__clothing-grid-item-right-popup-input input {
            width: 25.5vh;
            padding: 1vh;
            background: radial-gradient(rgba(255, 255, 255, .15), rgba(255, 255, 255, .05));
            border: .1vh solid rgba(255, 255, 255, .05);
            border-radius: .5vh;
            margin-top: 1vh;
            text-align: center;
            font-family: var(--ff-gilroy);
            font-size: 1.5vh;
            color: #fff;
        }
        
        .main__clothing-grid-item-right-popup-input input::placeholder {
            color: rgba(255, 255, 255, .5);
        }
        
        .main__clothing-confirm-btn-grid {
            width: 25.5vh;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
            margin-top: 1vh;
        }
        
        .main__clothing-confirm-btn-1 {
            width: 100%;
            padding: .75vh;
            font-size: 1.5vh;
            color: #fff;
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            border-radius: .25vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-confirm-btn-1:hover {
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .main__clothing-confirm-btn-2 {
            width: 100%;
            padding: .75vh;
            font-size: 1.5vh;
            color: #fff;
            border-radius: .5vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            border-radius: .25vh;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__clothing-confirm-btn-2:hover {
            box-shadow: 0vh 0vh 3vh #791515 inset, 0vh 0vh 1.7vh #fd1e1e;
            background: #923232;
            border: .1vh solid transparent;
        }
        
        .main__clothing-sphere {
            position: absolute;
            bottom: -10vh;
            left: 50%;
            transform: translateX(-50%);
            width: 35vh;
            height: 15vh;
            background: #ff0000;
            border-radius: 50%;
            filter: blur(12.5vh);
            z-index: -1;
        }
        
        .main__clothing-outfit-item-share-container {
            display: flex;
            align-items: center;
            gap: .25vh;
            margin-top: .25vh;
        }
        
        .main__coroelone-clothing-outfit-item-share-owner {
            font-size: 1vh;
            color: #fff;
            border: .1vh solid #fd0303;
            background: linear-gradient(0deg, #ff0000 0%, rgba(184, 28, 37, 0.00) 100%);
            box-shadow: 0vh 0vh 3.7vh 0vh #fd0303 inset, 0vh .4vh 3vh 0vh rgba(28, 96, 184, 0.55);
            padding-left: .5vh;
            padding-right: .5vh;
            border-radius: .25vh;
        }
        
        .main__clothing-outfit-item-share-name {
            font-size: 1vh;
            color: #fff;
            padding-left: .5vh;
            padding-right: .5vh;
            border-radius: .25vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.00) 100%);
            border: .1vh solid rgba(255, 255, 255, .05);
        }
        
        .main__clothing-handsup-btn {
            margin-top: 1vh;
            padding: 2vh;
            height: 5vh;
            border: .1vh solid rgba(255, 255, 255, .05);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .05) inset;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            color: #fff;
            border-radius: .5vh;
            cursor: pointer;
            gap: 1vh;
            transition: .2s ease-in;
        }
        
        .main__clothing-handsup-btn:hover {
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: .1vh solid transparent;
        }
        
        .absolute {
            position: absolute;
            bottom: 8vh;
            left: 2vh;
        }

        .main__clothing-scroll-item {
            width: 100%;
            height: 12vh;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%);
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__clothing-scroll-item:hover {
            background-size: 150%;
            background-position: center;
        }

        .main__clothing-scroll-item-name {
            font-family: var(--ff-bebas);
            font-size: 2vh;
            color: #fff;
            margin-top: 1vh;
        }

        .main__clothing-scroll-item-img {
            width: 7.5vh;
            height: 7vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__clothing-scroll-item-img img {
            width: 100%;
        }

        @keyframes hover {
            0% {
                transform: translate(-50%) translateY(-50%);
            }
            50% {
                transform: translate(-50%) translateY(-45%);
            }
            to {
                transform: translate(-50%) translateY(-50%);
            }
        }

        .saltynui-wrapper {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            position: absolute;
            left: 0;
            top: 0;
        }

        .saltynui-wrapper > .content {
            z-index: 100;
        }

        .saltynui-wrapper > .content > .bottom-salty {
            position: absolute;
            left: 50%;
            bottom: 11.8518518519vh;
            transform: translate(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .saltynui-wrapper > .content > .bottom-salty > .links {
            width: 90%;
            display: flex;
            justify-content: space-between;
            margin-top: 1.4814814815vh;
        }

        .saltynui-wrapper > .content > .bottom-salty > .links > .link {
            font-family: Akrobat;
            color: #ffffff8c;
            font-weight: 600;
        }

        .saltynui-wrapper > .content > .bottom-salty > .links > .link > i {
            margin-right: 0.7407407407vh;
        }

        .saltynui-wrapper > .content > .bottom-salty > .warning {
            width: 5.5555555556vh;
            aspect-ratio: 1/1;
        }

        .saltynui-wrapper > .content > .bottom-salty > .title {
            color: #ff4545;
            text-align: center;
            text-shadow: 0px 0px 2.8703703704vh #ff4545;
            font-family: Akrobat;
            font-size: 2.2222222222vh;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
        }

        .saltynui-wrapper > .content > .bottom-salty > .desc {
            margin-top: 0.2777777778vh;
            width: 41.6666666667vh;
            color: #ffffff8c;
            text-align: center;
            font-family: SVN-Gilroy;
            font-size: 1.2037037037vh;
            font-style: normal;
            font-weight: 400;
            line-height: 154%;
        }

        .saltynui-wrapper > .content > .bottom-salty > .loader-title {
            background: radial-gradient(122.9% 123.22% at 50.17% 50%, #3971d9 0%, rgba(57, 113, 217, 0) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: SVN-Gilroy;
            font-size: 2.2222222222vh;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            margin-bottom: 2.2222222222vh;
        }

        .saltynui-wrapper > .content > .bottom-salty > .loader {
            display: flex;
            gap: 1.1111111111vh;
            margin-bottom: 1.1111111111vh;
            margin-top: 1.1111111111vh;
        }

        .saltynui-wrapper > .content > .bottom-salty > .loader > .dot {
            width: 3.3333333333vh;
            aspect-ratio: 1/1;
            border-radius: 0.4937962963vh;
            background: radial-gradient(72.83% 64.17% at 50.17% 50%, rgba(57, 113, 217, 0.55) 0%, rgba(17, 31, 58, 0.55) 100%);
            -webkit-backdrop-filter: blur(11px);
            backdrop-filter: blur(11px);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .saltynui-wrapper > .content > .bottom-salty > .loader > .dot.active:before {
            opacity: 1;
        }

        .saltynui-wrapper > .content > .bottom-salty > .loader > .dot:before {
            transition: 0.25s;
            opacity: 0;
            content: "";
            display: block;
            width: 2.1296296296vh;
            aspect-ratio: 1/1;
            border-radius: 0.2777777778vh;
            border: 0.0925925926vh solid #3971d9;
            background: radial-gradient(36.56% 64.17% at 50.17% 50%, #3971d9 0%, #111f3a 100%);
            box-shadow: 0 0 3.4259259259vh #3971d9 inset, 0 0.3703703704vh 5.1851851852vh #3971d940;
        }
        
        .saltynui-wrapper > .content > .top {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%) translateY(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .saltynui-wrapper > .content > .top > .title {
            background: radial-gradient(122.9% 123.22% at 50.17% 50%, #3971d9 0%, rgba(57, 113, 217, 0) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: druk-trial;
            font-size: 14.0905555556vh;
            font-style: normal;
            font-weight: 500;
            line-height: 12.037037037vh;
            text-align: center;
        }

        .saltynui-wrapper > .content > .top > .desc {
            color: #ffffffc4;
            font-family: druk-trial;
            font-size: 4.6194444444vh;
            font-style: normal;
            font-weight: 500;
            line-height: 6.2962962963vh;
            letter-spacing: 2.9102777778vh;
            width: 92%;
        }

        .saltynui-wrapper > .saltynui {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%) translateY(-50%);
            width: 150vw;
            height: 150vh;
            background: url(https://corleone.vip/corleone/finalv8/web/img/newsalty/bkg.svg);
            background-size: cover;
            background-position: 20% 20%;
            animation: 3s infinite hover ease-in-out;
        }

        #animation {
            animation: hithere 2s ease infinite;
        }

        @keyframes hithere {
            30% {
                transform: scale(1.2);
            }
            40%,
            60% {
                transform: rotate(-20deg) scale(1.2);
            }
            50% {
                transform: rotate(20deg) scale(1.2);
            }
            70% {
                transform: rotate(0) scale(1.2);
            }
            to {
                transform: scale(1);
            }
        }

        .bg-salty {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: url(https://corleone.vip/corleone/finalv8/web/img/newsalty/bg.png);
            background-size: cover;
            background-position: center bottom;
            z-index: -1;
        }
        
        .bottom-salty {
            bottom: 5vh !important;
        }

        .bottom-salty img {
            width: 8vh;
        }

        .main__food-drink-container,
        .main__hud-carhud-container,
        .main__hud-top-ammonation-container,
        .main__hud-job-container,
        .main__hud-teamstats-container,
        .main__hud-location-container {
            cursor: move;
            position: absolute;
        }

        .main__fasocity-carpanel-container {
            position: absolute;
            bottom: 0.5vh;
            right: 0.5vh;
            width: 72vh;
            padding: 2vh;
            border-radius: 1vh;
            background: url(./img/carpanel/bg.png);
            background-size: cover;
            background-position: center top;
        }
        
        .main__fasocity-property-grid-item-header {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: var(--ff-bebas) !important;
            padding-bottom: 1vh;
        }

        .main__fasocity-property-grid-item-header-left {
            text-transform: uppercase;
            color: #ffffff80;
            font-size: 3vh;
            font-weight: 300;
        }

        .main__fasocity-property-grid-item-header-left p span {
            font-weight: 500;
            color: #fff;
        }

        .main__fasocity-property-grid-item-header-right-btn {
            width: 3.25vh;
            height: 3.25vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(to top, #ff0935, #be3b42);
            color: #fff;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: 0.2s ease-in;
            font-size: 1.5vh;
        }

        .main__fasocity-property-grid-item-header-right-btn i {
            transition: 0.2s ease-in;
        }

        .main__fasocity-property-grid-item-header-right-btn:hover {
            box-shadow: 0 0 1.7vh #be3b42;
        }

        .main__fasocity-property-grid-item-header-right-btn:hover i {
            transform: rotate(90deg);
        }

        .main__fasocity-property-grid-item-header-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0.1vh;
            background: linear-gradient(to right, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0));
        }

        .main__fasocity-property-grid-item-header-border-highlight {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 24.5vh;
            height: 0.1vh;
            background: #fd0303;
            box-shadow: 0 0 1.7vh #fd0303;
        }
        
        .main__fasocity-carpanel-grid-container {
            margin-top: 1.5vh;
            width: 100%;
            display: grid;
            grid-template-columns: 0.6fr 1fr;
            gap: 1vh;
        }
        
        .main__fasocity-carpanel-grid-item-left {
            width: 100%;
            height: 31.5vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-radius: 0.75vh;
            padding: 1vh;
        }
        
        .main__fasocity-carpanel-grid-item-left-header {
            width: 100%;
            display: grid;
            grid-template-columns: 1fr 0.2fr;
            gap: 0.25vh;
        }

        .main__fasocity-carpanel-grid-item-left-header-wave {
            width: 100%;
            display: grid;
            grid-template-columns: 0.65fr 0.35fr;
            gap: 0.25vh;
        }

        .main__fasocity-carpanel-grid-item-left-header-wave-left {
            position: relative;
            overflow: hidden;
            width: 100%;
            height: 3.6vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-radius: 0.5vh 0vh 0vh 0.5vh;
        }

        .main__fasocity-carpanel-grid-item-left-header-wave-right {
            width: 100%;
            height: 3.6vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-radius: 0vh 0.5vh 0.5vh 0vh;
        }

        .main__fasocity-carpanel-grid-item-left-btn-1 {
            width: 100%;
            height: 3.6vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-radius: 0.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.75vh;
            color: #fd0303;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-grid-item-left-btn-1.active {
            color: #fd0303;
            text-shadow: 0vh 0vh 1.7vh #fd0303;
        }

        .main__fasocity-carpanel-grid-item-left-btn-1.inactive:hover {
            box-shadow: 0 0 1.7vh #03a4fd inset;
            color: #ffffff80;
        }

        .main__fasocity-carpanel-grid-item-left-btn-1.active:hover {
            box-shadow: 0 0 1.7vh #fd0303 inset;
            color: #ffffff80;
        }

        .main__fasocity-carpanel-grid-item-left-btn-2 {
            width: 100%;
            height: 3.6vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-radius: 0.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.75vh;
            color: #ff0935;
            text-shadow: 0vh 0vh 1.7vh #ff0935;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-grid-item-left-btn-2:hover {
            box-shadow: 0 0 1.7vh #ff0935 inset;
            color: #ffffff80;
        }

        .main__fasocity-carpanel-grid-item-left-header-wave-left-img {
            position: absolute;
            bottom: 0vh;
            width: 200%;
            height: 5vh;
            background: url(./img/carpanel/wave.svg);
            background-size: cover;
            animation: 5s wave infinite;
        }

        @keyframes wave {
            0% {
                right: 0;
                height: 6vh;
            }
            50% {
                right: -100%;
                height: 6vh;
            }
            to {
                right: 0;
                height: 6vh;
            }
        }

        .main__fasocity-carpanel-grid-item-left-header-wave-right {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            color: #fff;
            font-family: var(--ff-bebas);
        }
        
        .main__fasocity-carpanel-car-container {
            position: relative;
            margin-top: 2.5vh;
            width: 100%;
            height: 17.5vh;
            background: url(./img/carpanel/car.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }

        .main__fasocity-carpanel-car-btn-container {
            position: relative;
            left: 50%;
            transform: translate(-50%);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5vh;
            margin-top: 0vh;
        }

        .main__fasocity-carpanel-car-btn,
        .main__fasocity-carpanel-car-btn-1,
        .main__fasocity-carpanel-car-btn-2 {
            position: relative;
            width: 5vh;
            height: 5vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-bottom: none;
            border-radius: 0.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-car-btn.active:hover {
            box-shadow: 0 0 4vh #3b7fbea8 inset;
        }

        .main__fasocity-carpanel-car-btn.inactive:hover {
            box-shadow: 0 0 4vh #be3b3ba8 inset;
        }

        .main__fasocity-carpanel-car-btn.active .main__fasocity-carpanel-car-stripe {
            background: #03a5fd56;
            box-shadow: 0 0 1.7vh #03a4fd;
        }

        .main__fasocity-carpanel-car-btn.inactive .main__fasocity-carpanel-car-stripe {
            background: #fd030356;
            box-shadow: 0 0 1.7vh #fd0303;
        }

        .main__fasocity-carpanel-car-btn-1.inactive:hover {
            box-shadow: 0 0 4vh #fd030356 inset;
        }

        .main__fasocity-carpanel-car-btn-1.active .main__fasocity-carpanel-car-stripe.green {
            background: #03fdcf56;
            box-shadow: 0 0 1.7vh #03fdd0;
        }

        .main__fasocity-carpanel-car-btn-1.inactive .main__fasocity-carpanel-car-stripe.green {
            background: #fd030356;
            box-shadow: 0 0 1.7vh #fd0303;
        }

        .main__fasocity-carpanel-car-btn-1.active:hover {
            box-shadow: 0 0 4vh #03fdcf56 inset;
        }

        .main__fasocity-carpanel-car-btn-2:hover {
            box-shadow: 0 0 4vh #b6e4374d inset;
        }

        .main__fasocity-carpanel-car-btn-2.active .main__fasocity-carpanel-car-stripe.yellow {
            background: #b6e4374d;
            box-shadow: 0 0 1.7vh #b7e437;
        }

        .main__fasocity-carpanel-car-btn-2.inactive .main__fasocity-carpanel-car-stripe.yellow {
            background: #e437374d;
            box-shadow: 0 0 1.7vh #e43737;
        }

        .main__fasocity-carpanel-car-btn-2.inactive:hover {
            box-shadow: 0 0 4vh #e437374d inset;
        }

        .main__fasocity-carpanel-car-btn-2.active:hover {
            box-shadow: 0 0 4vh #b6e4374d inset;
        }

        .main__fasocity-carpanel-car-stripe {
            position: absolute;
            bottom: 0.5vh;
            width: 3.5vh;
            height: 0.1vh;
            background: #03a5fd56;
            box-shadow: 0 0 1.7vh #03a4fd;
            transition: 0.2s ease-in;
            z-index: -1;
        }

        .main__fasocity-carpanel-car-stripe.green {
            background: #03fdcf56;
            box-shadow: 0 0 1.7vh #03fdd0;
        }

        .main__fasocity-carpanel-car-stripe.yellow {
            background: #b6e4374d;
            box-shadow: 0 0 1.7vh #b7e437;
        }

        .main__fasocity-carpanel-car-btn i {
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-car-icon {
            font-size: 2.5vh;
            color: #fff;
        }

        .main__fasocity-carpanel-car-icon img {
            width: 2.5vh;
        }

        .main__fasocity-carpanel-car-icon.img_1 img {
            width: 2.5vh;
            margin-bottom: 0.25vh;
        }

        .main__fasocity-carpanel-car-icon.img_2 img {
            width: 2.25vh;
            margin-bottom: 0.25vh;
        }

        .main__fasocity-carpanel-car-icon.img_3 img {
            width: 2vh;
            margin-bottom: 0.25vh;
        }

        .main__fasocity-carpanel-car-btn:hover i {
            color: #ff0935;
            text-shadow: 0vh 0vh 1.7vh #ff0935;
        }

        .main__fasocity-carpanel-car-btn:hover .main__fasocity-carpanel-car-stripe,
        .main__fasocity-carpanel-car-btn-1:hover .main__fasocity-carpanel-car-stripe,
        .main__fasocity-carpanel-car-btn-2:hover .main__fasocity-carpanel-car-stripe {
            height: 2vh;
            z-index: -1;
            border-radius: 0.5vh 0.5vh 0vh 0vh;
        }

        .main__fasocity-carpanel-car-btn-top {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: -1.25vh;
            left: 50%;
            transform: translate(-50%);
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-bottom {
            position: absolute;
            width: 3vh;
            height: 3vh;
            bottom: 2vh;
            left: 50%;
            transform: translate(-50%);
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-bottom.inactive,
        .main__fasocity-carpanel-car-btn-right-bottom.inactive,
        .main__fasocity-carpanel-car-btn-top.inactive,
        .main__fasocity-carpanel-car-btn-left-bottom.inactive,
        .main__fasocity-carpanel-car-btn-left-top.inactive,
        .main__fasocity-carpanel-car-btn-right-top.inactive {
            background: url(./img/carpanel/red_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }

        .main__fasocity-carpanel-car-btn-bottom.active,
        .main__fasocity-carpanel-car-btn-right-bottom.active,
        .main__fasocity-carpanel-car-btn-top.active,
        .main__fasocity-carpanel-car-btn-left-bottom.active,
        .main__fasocity-carpanel-car-btn-left-top.active,
        .main__fasocity-carpanel-car-btn-right-top.active {
            opacity: 0.25;
        }

        .main__fasocity-carpanel-car-btn-right-top {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: 2vh;
            right: 2vh;
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-left-top {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: 2vh;
            left: 2vh;
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-right-bottom {
            position: absolute;
            width: 3vh;
            height: 3vh;
            bottom: 4vh;
            right: 2vh;
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-left-bottom {
            position: absolute;
            width: 3vh;
            height: 3vh;
            bottom: 4vh;
            left: 2vh;
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-middle-top-right {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin-top: -3vh;
            margin-left: 2vh;
            background: url(./img/carpanel/blue_btn-blue.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-middle-top-left {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin-top: -3vh;
            margin-left: -2vh;
            background: url(./img/carpanel/blue_btn-blue.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-middle-top-left.inactive,
        .main__fasocity-carpanel-car-btn-middle-top-left.inactive2,
        .main__fasocity-carpanel-car-btn-middle-bottom-left.inactive,
        .main__fasocity-carpanel-car-btn-middle-bottom-left.inactive2,
        .main__fasocity-carpanel-car-btn-middle-bottom-right.inactive,
        .main__fasocity-carpanel-car-btn-middle-bottom-right.inactive2,
        .main__fasocity-carpanel-car-btn-middle-top-right.inactive,
        .main__fasocity-carpanel-car-btn-middle-top-right.inactive2 {
            background: url(./img/carpanel/red_btn-red.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: not-allowed;
        }

        .main__fasocity-carpanel-car-btn-middle-bottom-left {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin-top: 1vh;
            margin-left: -2vh;
            background: url(./img/carpanel/blue_btn-blue.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-middle-bottom-right {
            position: absolute;
            width: 3vh;
            height: 3vh;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin-top: 1vh;
            margin-left: 2vh;
            background: url(./img/carpanel/blue_btn-blue.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: 0.2s ease-in;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-top:not(.inactive):not(.inactive2):not(.active):hover,
        .main__fasocity-carpanel-car-btn-bottom:not(.inactive):not(.inactive2):not(.active):hover,
        .main__fasocity-carpanel-car-btn-right-top:not(.inactive):not(.inactive2):not(.active):hover,
        .main__fasocity-carpanel-car-btn-left-top:not(.inactive):not(.inactive2):not(.active):hover,
        .main__fasocity-carpanel-car-btn-right-bottom:not(.inactive):not(.inactive2):not(.active):hover,
        .main__fasocity-carpanel-car-btn-left-bottom:not(.inactive):not(.inactive2):not(.active):hover {
            opacity: 0.5;
        }
        
        .main__fasocity-carpanel-car-btn-img {
            width: 1.1vh;
            height: 1.1vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__fasocity-carpanel-car-btn-img img {
            width: 100%;
        }

        .main__fasocity-carpanel-grid-item-right.inactive {
            opacity: 0.5;
        }

        .main__fasocity-carpanel-grid-item-right {
            width: 100%;
            padding: 1vh;
            border-radius: 0.75vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-grid-item-right-header {
            display: flex;
            align-items: center;
            gap: 0.5vh;
            display: grid;
            grid-template-columns: 0.25fr 1fr;
        }

        .main__fasocity-carpanel-grid-item-right-header-left {
            width: 8vh;
            height: 8vh;
            border-radius: 0.5vh;
            background: black;
        }

        .main__fasocity-carpanel-grid-item-right-header-left img {
            width: 100%;
            height: 100%;
            border-radius: 0.5vh;
        }

        .main__fasocity-carpanel-grid-item-right-header-right {
            position: relative;
            width: 100%;
            height: 8vh;
            border-radius: 0.5vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            padding: 1vh;
        }

        .song_name {
            font-family: var(--ff-bebas);
            font-size: 1.5vh;
            color: #fff;
            font-weight: 600;
        }

        .song_artist {
            font-size: 1.25vh;
            color: #ffffff80;
        }

        .main__fasocity-carpanel-grid-item-right-header-progress {
            width: 100%;
        }

        .main__fasocity-carpanel-grid-item-right-header-progress-text {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1vh;
            color: #ffffff80;
            margin-top: 0.5vh;
        }

        .main__fasocity-carpanel-grid-item-right-header-progress-1 {
            width: 100%;
            height: 0.35vh;
            border-radius: 5vh;
            background: rgba(255, 255, 255, 0.05);
            margin-top: 0.5vh;
        }

        .main__fasocity-carpanel-grid-item-right-header-progress-inner {
            width: 0%;
            height: 100%;
            background: #03a4fd;
            box-shadow: 0 0 1.7vh #03a4fd;
            border-radius: 5vh;
        }

        .main__fasocity-carpanel-play-btn-container {
            position: absolute;
            top: 1vh;
            right: 1vh;
            display: flex;
            align-items: center;
            gap: 0.5vh;
        }

        .main__fasocity-carpanel-play-btn {
            width: 3vh;
            height: 3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1vh;
            color: #fff;
            background: url(./img/carpanel/blue_btn-notactive.png);
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-play-btn:hover {
            background: url(./img/carpanel/blue_btn.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }

        .main__fasocity-carpanel-grid-item-spotify-link-container {
            width: 100%;
            margin-top: 0.5vh;
            padding: 0.5vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 0.5vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
        }

        .main__fasocity-carpanel-grid-item-spotify-link-icon {
            font-size: 1.75vh;
            color: #67fb81;
            text-shadow: 0vh 0vh 1.7vh #67fb81;
        }

        .main__fasocity-carpanel-grid-item-spotify-link-icon img {
            width: 2.5vh;
            margin-top: 0.5vh;
        }
        
        .main__fasocity-carpanel-grid-item-spotify-link {
            width: 30vh;
            border-bottom: 0.1vh solid rgba(255, 255, 255, 0.05);
            height: 2.5vh;
        }

        .main__fasocity-carpanel-grid-item-spotify-link input {
            font-family: var(--ff-gilroy);
            color: #fff;
            width: 100%;
            font-size: 1.25vh;
            background: transparent;
            border: none;
            outline: none;
        }

        .main__fasocity-carpanel-grid-item-spotify-link input {
            font-family: var(--ff-gilroy) !important;
            color: #fff !important;
            font-size: 1.25vh !important;
        }

        .main__fasocity-carpanel-grid-item-spotify-link-btn {
            height: 2.5vh;
            padding-left: 1vh;
            padding-right: 1vh;
            background: rgba(103, 251, 129, 0.22);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 0.2vh;
            font-size: 1.25vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            color: #67fb81;
            text-shadow: 0vh 0vh 0.91vh #67fb81;
        }

        .main__fasocity-carpanel-grid-item-spotify-link-btn:hover {
            box-shadow: 0 0 1.7vh #67fb81 inset;
        }

        .main__fasocity-carpanel-playlist-header {
            margin-top: 1vh;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.25vh;
            color: #ffffff80;
        }

        .main__fasocity-carpanel-playlist-header p:first-child {
            color: #67fb81;
            text-shadow: 0vh 0vh 1.7vh #67fb81;
        }

        .main__fasocity-carpanel-playlist-scroll-container {
            width: 100%;
            height: 13vh;
            margin-top: 0.5vh;
            padding-right: 1vh;
            overflow-y: scroll;
        }

        .main__fasocity-carpanel-playlist-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
        }

        .main__fasocity-carpanel-playlist-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
        }

        .main__fasocity-carpanel-playlist-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            background: #03a4fd;
        }

        .main__fasocity-carpanel-playlist-scroll-item {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 0.5vh;
            margin-top: 0.5vh;
            opacity: 0.5;
            transition: 0.2s ease-in;
        }

        .main__fasocity-carpanel-playlist-scroll-item.active {
            opacity: 1;
        }

        .main__fasocity-carpanel-playlist-scroll-item:first-child {
            margin-top: 0vh;
        }

        .main__fasocity-carpanel-playlist-scroll-item-img {
            width: 3.5vh;
            height: 3.5vh;
            border-radius: 0.25vh;
        }

        .main__fasocity-carpanel-playlist-scroll-item-img img {
            width: 100%;
            height: 100%;
            border-radius: 0.25vh;
        }

        .main__fasocity-carpanel-playlist-scroll-item-name {
            width: 87.5%;
            height: 3.5vh;
            border-radius: 0.5vh;
            background: radial-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            font-size: 1.15vh;
            color: #ffffff80;
            padding-left: 1vh;
            padding-right: 1vh;
        }

        .main__fasocity-carpanel-playlist-scroll-item-name p:first-child {
            margin-top: 0.2vh;
            color: #fff;
        }

        .main__fasocity-carpanel-playlist-scroll-item-name p:last-child {
            font-size: 1.05vh;
        }

        .main__fasocity-carpanel-playlist-scroll-item-btn {
            width: 2vh;
            height: 3.5vh;
            border: 0.1vh solid #e63346;
            background: radial-gradient(120.83% 120.83% at 50% 0%, rgba(230, 51, 70, 0.15) 0%, rgba(230, 51, 70, 0) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #e63346;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
            border-radius: 0.5vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            font-size: 1.25vh;
        }
        
        .main__fasocity-carpanel-playlist-scroll-item-btn:hover {
            box-shadow: 0 0 1.7vh #e63346 inset;
        }

        .main__fasocity-property-grid-item-range {
            position: absolute;
            bottom: 1.75vh;
            right: 4vh;
            display: flex;
            align-items: center;
            gap: 0.5vh;
            font-size: 1.25vh;
            color: #ffffff80;
        }

        .main__fasocity-property-grid-item-range input {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 6.5vh;
            height: 0.3vh;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5vh;
        }

        .main__fasocity-property-grid-item-range input::-webkit-slider-thumb {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 1vh;
            height: 1vh;
            border-radius: 0.5vh;
            background: #fd0303;
            box-shadow: 0 0 1.7vh #fd0303;
            cursor: pointer;
        }

        .main__referral-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1vh;
        }

        .main__referral-container-left {
            width: 80vh;
            border-radius: 1vh;
            padding: 2vh;
            background: url(img/main_bg/bg_4.png);
            background-size: cover;
        }

        .main__referral-container-right {
            width: 30vh;
            border-radius: 1vh;
            padding: 2vh;
            background: url(img/main_bg/bg_4.png);
            background-size: cover;
        }

        .main__referral-left-content-container {
            margin-top: 1.5vh;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr 0.5fr;
            gap: 1vh;
        }

        .main__referral-left-content-container-left-grid-item {
            width: 100%;
            padding: 0.5vh;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
        }

        .main__referral-left-content-container-left-grid-item-2 {
            width: 100%;
            padding: 0.5vh;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            margin-top: 1vh;
        }

        .main__referral-left-content-container-left-grid-item-inner {
            width: 100%;
            padding: 1vh;
            border-radius: 0.25vh;
            box-shadow: 0 0 10vh #ffffff26 inset;
            height: 40vh;
        }

        .main__referral-left-content-container-left-grid-item-inner-2 {
            width: 100%;
            padding: 1vh;
            border-radius: 0.25vh;
            box-shadow: 0 0 3vh #ffffff26 inset;
        }

        .main__referral-left-content-container-left-grid-item-header {
            font-size: 1.5vh;
            font-family: var(--ff-header);
            color: #fff;
            text-shadow: 0vh 0vh 1.7vh #fff;
        }

        .main__referral-left-content-container-left-grid-item-text-container {
            display: flex;
            flex-direction: column;
            gap: 1vh;
            margin-top: 0.5vh;
        }

        .main__referral-left-content-container-left-grid-item-text-block {
            font-size: 1.1vh;
            color: #ffffff80;
        }

        .main__referral-left-content-container-left-grid-item-player-container {
            margin-top: 0.5vh;
            height: 35.5vh;
            padding-right: 0.5vh;
            display: flex;
            flex-direction: column;
            gap: 0.5vh;
            overflow-y: scroll;
        }

        .main__referral-left-content-container-left-grid-item-player-container::-webkit-scrollbar {
            width: 0.3vh;
            border-radius: 1vh;
        }

        .main__referral-left-content-container-left-grid-item-player-container::-webkit-scrollbar-track {
            width: 0.3vh;
            border-radius: 1vh;
            background: #ffffff0d;
        }

        .main__referral-left-content-container-left-grid-item-player-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            border-radius: 1vh;
            background: #ff2323;
        }

        .main__referral-left-content-container-left-grid-item-player-item {
            width: 100%;
            padding: 0.25vh;
            border-radius: 0.25vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            gap: 0.5vh;
        }

        .main__referral-left-content-container-left-grid-item-player-item-left-icon {
            width: 3vh;
            height: 3vh;
            border-radius: 0.1vh;
            background: #ff2323;
            box-shadow: 0 0 1.7vh #ffffff80 inset;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25vh;
        }

        .main__referral-container-color {
            font-size: 1.1vh;
            color: #ffffff80;
        }

        .main__referral-left-content-container-left-grid-item-player-item-right-progress-container {
            display: flex;
            align-items: center;
            gap: 0.5vh;
            font-size: 1.1vh;
            color: #fff;
            line-height: 1.1vh;
        }

        .main__referral-left-content-container-left-grid-item-player-item-right-progress-container-inner {
            width: 12vh;
            height: 0.5vh;
            background: #ffffff26;
            border-radius: 1vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
        }

        .main__referral-left-content-container-left-grid-item-player-item-right-progress-container-inner-fill {
            width: 50%;
            border-radius: 1vh;
            background: #ff2525;
            height: 100%;
            box-shadow: 0 0 1.7vh #ff2525;
        }

        .main__referral-left-content-container-left-grid-item-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .main__referral-left-content-container-left-grid-item-flex-left-item {
            padding: 0.25vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            font-size: 1.25vh;
            color: #fff;
            border-radius: 0.25vh;
        }

        .main__referral-left-content-container-left-grid-item-flex-left-item-code {
            padding-left: 1vh;
            padding-right: 1vh;
        }

        .main__referral-left-content-container-left-grid-item-flex-left-item {
            display: flex;
            align-items: center;
            gap: 1vh;
            height: 3.75vh;
        }

        .main__referral-left-content-container-left-grid-item-flex-left-item-code p span {
            color: #ff2525;
            text-shadow: 0vh 0vh 1.7vh #ff2525;
        }

        .main__referral-container-right-scroll-container {
            width: 100%;
            padding-right: 0.5vh;
            height: 49vh;
            margin-top: 1.5vh;
            overflow-y: scroll;
            display: flex;
            flex-direction: column;
            gap: 0.5vh;
        }

        .main__referral-container-right-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
            border-radius: 1vh;
        }

        .main__referral-container-right-scroll-container::-webkit-scrollbar-track {
            width: 0.3vh;
            border-radius: 1vh;
            background: #ffffff0d;
        }

        .main__referral-container-right-scroll-container::-webkit-scrollbar-thumb {
            width: 0.3vh;
            border-radius: 1vh;
            background: #ff2323;
        }

        .main__referral-container-right-scroll-item {
            width: 100%;
            padding: 0.5vh;
            border-radius: 0.5vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
        }

        .main__referral-container-right-scroll-item-inner {
            width: 100%;
            padding: 1vh;
            border-radius: 0.25vh;
            box-shadow: 0 0 3vh #ffffff26 inset;
        }

        .main__referral-container-right-scroll-item-inner-header {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__referral-container-right-scroll-item-inner-header-icon {
            width: 2.5vh;
            height: 2.5vh;
            font-size: 1.25vh;
            color: #fff;
            border-radius: 0.25vh;
            border: 0.1vh solid #ff2323;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 0 1.7vh #ff2323 inset;
        }

        .header {
            font-size: 1.1vh;
            color: #fff;
        }

        .main__referral-container-right-scroll-item-inner-text {
            margin-top: 0.5vh;
            font-size: 1.1vh;
            color: #ffffff80;
        }

        .main__referral-container-right-scroll-item-inner-text p span {
            color: #03fd74;
            text-shadow: 0vh 0vh 1.7vh #03fd74;
        }

        .mt-2 {
            margin-top: 0.5vh;
            text-align: center;
        }

        .main__referral-left-content-container-left-grid-item-player-item-right-progress-text {
            white-space: nowrap;
            font-size: 1vh;
        }

        .main__corleone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main__corleone-header p:last-child {
            color: #ffffff80;
        }

        .main__corleone-header-new-left {
            font-family: var(--ff-bebas);
            font-size: 2.75vh;
            color: #fff;
            font-weight: 300;
            color: #ffffff80;
        }

        .main__corleone-header-new-left-2 {
            font-family: var(--ff-bebas);
            font-size: 2.25vh;
            color: #fff;
            font-weight: 300;
            color: #ffffff80;
        }

        .main__corleone-header-new-left p span,
        .main__corleone-header-new-left-2 p span {
            font-weight: 500;
            color: #fff;
        }

        .main__corleone-header-new-line {
            position: relative;
            margin-top: 1vh;
            width: 100%;
            height: 0.1vh;
            background: linear-gradient(to right, #ffffff0d, #fff0);
        }

        .main__corleone-header-new-line-highlight {
            position: absolute;
            top: 0vh;
            left: 0vh;
            width: 18.5vh;
            height: 100%;
            background: #ff0000;
            box-shadow: 0 0 1.7vh #ff0000;
        }

        .main__corleone-header-new-line-highlight.orange {
            background: #ff0000;
            box-shadow: 0 0 1.7vh #ff0000;
        }

        .main__corleone-header-new-container-right-close-container {
            width: 2.75vh;
            height: 2.75vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid #ff3055;
            background: linear-gradient(0deg, #ff0935, #b81c2500);
            box-shadow: 0 0 3.7vh #ee4b69 inset, 0 0.4vh 3vh #b81c258c;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.3vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            border-radius: 0.3vh;
            color: #fff;
        }

        .main__corleone-header-new-container-right-close-container i {
            transition: 0.2s ease-in;
        }

        .main__corleone-header-new-container-right-close-container:hover {
            opacity: 0.75;
            box-shadow: 0 0 3.7vh #ee4b69 inset, 0 0.4vh 5vh #ff1723;
        }

        .main__corleone-header-new-container-right-close-container:hover i {
            transform: rotate(90deg);
        }

        .main__corleone-header-new-right {
            display: flex;
            align-items: center;
            gap: 1vh;
        }

        .main__corleone-header-new-container-right-focus-container {
            width: 2.75vh;
            height: 2.75vh;
            border-radius: var(--border-radius-close);
            border: 0.1vh solid #ffac30;
            background: linear-gradient(0deg, #ffac30, #b81c2500);
            box-shadow: 0 0 3.7vh #ffac30 inset, 0 0.4vh 3vh #b87c1c8c;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-white);
            font-size: 1.3vh;
            cursor: pointer;
            transition: 0.2s ease-in;
            border-radius: 0.3vh;
            color: #fff;
        }

        .main__corleone-header-new-container-right-focus-container i {
            transition: 0.2s ease-in;
        }

        .main__corleone-header-new-container-right-focus-container:hover {
            opacity: 0.75;
            box-shadow: 0 0 3.7vh #ffac30 inset, 0 0.4vh 5vh #ff9a17;
        }

        .main__corleone-header-new-container-right-focus-container:hover i {
            transform: rotate(90deg);
        }

        .main__corleone-button-btn-back {
            gap: 0.5vh;
            font-family: var(--ff-bebas);
            font-size: 1.65vh;
            letter-spacing: 0.15vh;
            color: #ffffff80;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            border-radius: 0vh 0.25vh 0.25vh 0vh;
            cursor: not-allowed;
            transition: 0.2s ease-in;
            background: #ffffff0d;
            z-index: 100;
            overflow: hidden;
            text-transform: uppercase;
            position: relative;
            text-align: center;
            padding: 0.5vh 1vh;
            width: 100%;
            height: 3.1vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__corleone-button-item-kopieren,
        .main__corleone-button-item {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.5vh 1vh;
            font-size: 1.4vh;
            color: #fff;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 1.7vh #ffffff26 inset;
            cursor: pointer;
            transition: 0.2s ease-in;
            text-transform: uppercase;
            font-family: var(--ff-bebas);
            letter-spacing: 0.15vh;
            overflow: hidden;
            z-index: 100;
            border-radius: 0vh 0.25vh 0.25vh 0vh;
            width: 100%;
            height: 3.1vh;
        }

        .main__corleone-button-item-kopieren:hover,
        .main__corleone-button-item:hover,
        .main__corleone-button-item.active {
            text-shadow: 0vh 0vh 1.7vh #fff;
        }

        .main__corleone-button-item-gradient {
            position: absolute;
            width: 100%;
            height: 0%;
            bottom: 0vh;
            left: 0vh;
            background: linear-gradient(to top, #ff0000, #0000);
            z-index: -1;
            transition: 0.2s ease-in;
        }

        .main__corleone-button-item-gradient.red {
            background: linear-gradient(to top, #ff0000, #0000);
        }

        .main__corleone-button-item-gradient.orange {
            background: linear-gradient(to top, #ff0000, #0000);
        }

        .main__corleone-button-item-gradient.purple {
            background: linear-gradient(to top, #ff0000, #0000);
        }

        .main__corleone-button-item:hover .main__corleone-button-item-gradient,
        .main__corleone-button-item.active .main__corleone-button-item-gradient {
            height: 100%;
        }

        .main__corleone-button-item-indicator {
            position: absolute;
            top: 0.25vh;
            left: 50%;
            transform: translate(-50%);
            width: 0vh;
            height: 0.1vh;
            background: #ff0000;
            box-shadow: 0 0 1.7vh #ff0000;
            transition: 0.2s ease-in;
        }

        .main__corleone-button-item-indicator.red {
            background: #ff0000;
            box-shadow: 0 0 1.7vh #ff0000;
        }

        .main__corleone-button-item-indicator.orange {
            background: #ff0000;
            box-shadow: 0 0 1.7vh #ff0000;
        }

        .main__corleone-button-item-indicator.purple {
            background: #ff0000;
            box-shadow: 0 0 1.7vh #ff0000;
        }

        .main__corleone-button-item:hover .main__corleone-button-item-indicator,
        .main__corleone-button-item.active .main__corleone-button-item-indicator {
            width: 4vh;
        }

        .main__bag_container {
            width: 100%;
            height: 100%;
            border: none;
        }

        .main__bag_container img {
            width: auto;
            height: auto;
            min-width: 100%;
            min-height: 100%;
        }

        .main__michy_container {
            font-weight: normal;
            font-family: Arial;
            font-size: 1.5vmin;
            font-weight: 800;
            color: white;
            text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.8), -1px 1px 0px rgba(0, 0, 0, 0.8), 1px -1px 0px rgba(0, 0, 0, 0.8), -1px -1px 0px rgba(0, 0, 0, 0.8);
            user-select: none;
            text-align: left;
        }

        .main__michy_container input {
            background: rgba(0, 0, 0, 0.4);
            color: white;
            border: none;
            outline: none;
            user-select: all;
        }

        .main__michy_container button {
            background: rgba(255, 255, 255, 0.8);
            padding: 0.5vmin;
            color: black;
            border: none;
            outline: none;
            font-weight: 700;
            border-radius: 4px;
            margin-top: 1vmin;
            margin-bottom: 1vmin;
        }

        .main__michy_container button:active {
            background: rgba(128, 192, 255, 0.8);
        }

        #michy_clipboard {
            position: absolute;
            color: transparent;
            background: transparent;
            border: transparent;
            outline: transparent;
            pointer-events: none;
            resize: none;
        }

        #michy_content {
            display: block;
            position: absolute;
            left: 50vw;
            top: 6vmin;
            padding: 0px;
            margin: 0px;
            tab-size: 0px;
            max-height: 80vh;
            transform: translate(-50%, 0%);
        }

        #michy_handling-fields {
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 50vw;
            max-height: 50vh;
        }

        #michy_handling-fields div {
            display: flex;
            justify-content: space-between;
            margin-right: 2vmin;
        }

        .michy_tooltip .michy_tooltip-text {
            visibility: hidden;
            width: 50vw;
            background-color: rgba(0, 0, 0, 0.4);
            color: white;
            text-align: left;
            padding: 1vmin 1vmin;
            border-radius: 1vmin;
            position: absolute;
            z-index: 1;
            top: 110%;
            left: 50%;
            opacity: 0;
            transform: translate(-50%, 0%);
        }

        .michy_tooltip .michy_tooltip-text::after {
            content: "";
            position: absolute;
        }

        .michy_tooltip:hover .michy_tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .main__corleone-deathscreen-container {
            width: 100%;
            height: 100vh;
            background: url(img/deathscreen/bg.png);
            background-size: cover;
            background-position: center;
            padding: 4vh;
        }
        
        .main__corleone-deathscreen-content-container {
            width: 100%;
            height: 100%;
            border: .1vh solid rgba(255, 255, 255, .15);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        .main__corleone-deathscreen-content-header {
            font-family: var(--ff-gangwar);
            text-align: center;
        }
        
        .main__corleone-deathscreen-content-header p:first-child {
            font-size: 17vh;
            text-transform: uppercase;
            background: -webkit-radial-gradient(#eee, #333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .main__corleone-deathscreen-content-header p:last-child {
            font-size: 10vh;
            text-transform: uppercase;
            background: -webkit-radial-gradient(#fd0303, #680000);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: -4vh;
        }
        
        .main__corleone-deathscreen-content-stripe {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 5.2vh;
            height: .2vh;
            background: #d93939;
            bottom: 4vh;
            box-shadow: 0vh 0vh 1.7vh #d93939;
        }
        
        .main__corleone-deathscreen-content-grid-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            margin-top: 1vh;
        }
        
        .main__corleone-deathscreen-content-grid-item {
            display: flex;
            align-items: center;
            transition: .2s ease-in;
            margin-top: 1vh;
        }
        
        .main__corleone-deathscreen-content-grid-item-icon {
            width: 3vh;
            height: 3vh;
            border: .2vh solid rgba(255, 255, 255, .55);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5vh;
            color: #fd0303;
            text-shadow: 0vh 0vh 1.7vh #fd0303;
        }
        
        .main__corleone-deathscreen-content-grid-item-content {
            height: 3vh;
            width: 25vh;
            display: flex;
            align-items: center;
            gap: 2vh;
            background: url(img/deathscreen/bg_2.png);
            background-size: cover;
            background-position: center left;
            font-family: var(--ff-gilroy);
            font-size: 1.5vh;
            color: var(--color-white);
        }
        
        .main__corleone-deathscreen-content-grid-item-content-text {
            margin-left: 2vh;
            text-overflow: ellipsis;
            text-wrap: nowrap;
            white-space: nowrap;
        }
        
        .main__corleone-deathscreen-content-grid-item-content-info {
            margin-left: 3vh;
            padding-right: 2vh;
            text-overflow: ellipsis;
            text-wrap: nowrap;
            white-space: nowrap;
        }
        
        .main__corleone-deathscreen-content-grid-item-content-2 {
            height: 3vh;
            display: flex;
            align-items: center;
            gap: 2vh;
            background: url(img/deathscreen/bg_3.png);
            background-size: cover;
            background-position: center left;
            font-family: var(--ff-gilroy);
            font-size: 1.5vh;
            color: var(--color-white);
        }
        
        .main__corleone-deathscreen-content-grid-container-2 {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 2vh;
            gap: 3vh;
        }
        
        .main__corleone-deathscreen-content-grid-item-content.br-full {
            border-radius: 0vh 5vh 5vh 0vh;
        }
        
        .main__corleone-deathscreen-content-grid-item-content-info-2 {
            margin-left: 3vh;
            width: 6vh;
            display: flex;
            justify-content: center;
        }

        .main__banking-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 79vh;
            padding: 3vh;
            border-radius: var(--border-radius-frame);
            background: url(img/banking/bg.png);
            background-size: cover;
            background-position: center;
            overflow: hidden;
        }
        
        .main__banking-top-grid-container {
            width: 100%;
            margin-top: 2vh;
            height: 19vh;
            display: grid;
            grid-template-columns: 0.7fr 1fr;
            gap: 1vh;
        }

        .main__banking-top-grid-left {
            position: relative;
            width: 100%;
            height: 100%;
            background: url(img/banking/card.png);
            background-size: cover;
            border-radius: 0.4vh;
            padding: 1.5vh;
            overflow: hidden;
        }

        .main__banking-top-grid-right {
            position: relative;
            width: 100%;
            height: 100%;
            background: url(img/banking/graph.png);
            background-size: cover;
            border-radius: 0.4vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.05);
        }

        .main__banking-top-grid-left-text-header {
            text-transform: uppercase;
            font-size: 1.5vh;
            color: var(--color-white);
            font-weight: 600;
        }

        .main__banking-top-grid-left-text-iban {
            font-family: var(--ff-header);
            font-size: 1.5vh;
            font-weight: 500;
            color: var(--color-white);
            letter-spacing: 0.35vh;
            margin-top: 2vh;
        }

        .main__banking-top-grid-bottom-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2vh;
        }

        .main__banking-top-grid-bottom-left p,
        .main__banking-top-grid-bottom-right p {
            font-size: 1.2vh;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
        }

        .main__banking-top-grid-bottom-left p:nth-last-child(1),
        .main__banking-top-grid-bottom-right p:nth-last-child(1) {
            font-size: 1.4vh;
            color: var(--color-white);
            text-transform: uppercase;
        }

        .main__banking-top-grid-blur-container {
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            width: 100%;
            padding: 1vh;
            padding-left: 1.5vh;
            padding-right: 1.5vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
            fill: rgba(0, 0, 0, 0.09);
            background: rgba(0, 0, 0, 0.25);
            border-radius: 0vh 0vh 0.4vh 0.4vh;
        }

        .main__banking-top-grid-blur-right {
            display: flex;
            align-items: center;
            gap: 0.75vh;
        }

        .main__banking-top-grid-blur-right-text p {
            font-size: 1.2vh;
            color: rgba(255, 255, 255, 0.5);
            text-transform: uppercase;
            text-align: right;
        }

        .main__banking-top-grid-blur-right-text p:nth-last-child(1) {
            font-size: 1.4vh;
            color: var(--color-white);
        }

        .main__banking-top-grid-blur-right-icon {
            width: 3.2vh;
            height: 3.2vh;
            border-radius: 0.3vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2vh;
            color: var(--color-white);
            background: rgba(0, 0, 0, 0.16);
        }

        .main__banking-top-grid-blur-left img {
            width: 5vh;
        }

        .main__banking-middle-grid-container {
            width: 100%;
            position: relative;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1vh;
            height: 17.6vh;
            margin-top: 2vh;
        }

        .main__banking-middle-grid-item {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 0.4vh;
            overflow: hidden;
        }

        .main__banking-middle-grid-item-container {
            width: 100%;
            position: absolute;
            top: 0vh;
            left: 0vh;
            display: flex;
            flex-direction: column;
            transition: 0.1s ease-in;
        }

        .main__banking-middle-grid-item-1 {
            position: relative;
            width: 100%;
            height: 17.6vh;
            background: url(img/banking/money_bg-1.png);
            background-size: cover;
        }

        .main__banking-middle-grid-item-2 {
            position: relative;
            width: 100%;
            height: 17.6vh;
            background: url(img/banking/money_bg-1-2.png);
            background-size: cover;
        }

        .main__banking-middle-grid-item-1.deposit {
            background: url(img/banking/money_bg-2-1.png);
            background-size: cover;
        }

        .main__banking-middle-grid-item-2.deposit {
            background: url(img/banking/money_bg-2-2.png);
            background-size: cover;
        }

        .main__banking-middle-grid-item-1.withdraw {
            background: url(img/banking/money_bg-3-1.png);
            background-size: cover;
        }

        .main__banking-middle-grid-item-2.withdraw {
            background: url(img/banking/money_bg-3-2.png);
            background-size: cover;
        }

        .main__banking-middle-grid-item-1-icon {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main__banking-middle-grid-item-1-icon.withdraw {
            margin-top: -1vh;
        }
        
        .main__banking-middle-grid-item-1-icon img {
            margin-top: 2vh;
            width: 11.3vh;
        }

        .main__banking-middle-grid-item-einzahlen-btn,
        .main__banking-middle-grid-item-auszahlen-btn,
        .main__banking-middle-grid-item-transfer-btn {
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            width: 100%;
            padding: 1.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.4vh;
            text-transform: uppercase;
            color: var(--color-white);
            background: rgba(18, 18, 18, 0.1);
            cursor: pointer;
            transition: 0.2s ease-out;
            font-weight: 600;
        }

        .main__banking-middle-grid-item-einzahlen-btn:hover,
        .main__banking-middle-grid-item-auszahlen-btn:hover,
        .main__banking-middle-grid-item-transfer-btn:hover {
            box-shadow: 0vh 0vh 1.7vh rgba(0, 0, 0, 0.5) inset;
            text-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.75);
        }

        .main__banking-middle-grid-item-container.active {
            top: -17.6vh;
        }

        .main__banking-middle-grid-item-2 {
            padding: 1.5vh;
        }

        .main__banking-middle-grid-item-2-header {
            text-transform: uppercase;
            font-weight: 600;
            font-size: 1.4vh;
            color: var(--color-white);
        }

        .main__banking-middle-grid-item-2-header p:nth-last-child(1) {
            font-size: 1.3vh;
            text-transform: none;
            color: rgba(255, 255, 255, 0.5);
            font-weight: 400;
        }

        .main__banking-middle-grid-item-2-interact-container {
            margin-top: 2.1vh;
        }

        .main__banking-middle-grid-item-2-interact-container.transfer {
            margin-top: -0.15vh;
        }

        .main__banking-middle-grid-item-2-interact-container-2 {
            width: 100%;
            position: relative;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
        }

        .main__banking-middle-grid-item-2-interact-input input {
            width: 100%;
            text-align: center;
            font-size: 1.3vh;
            border-radius: 0.4vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            background: rgba(255, 255, 255, 0.14);
            padding: 1vh;
            color: var(--color-white);
            font-family: var(--ff-header);
            outline: none;
        }

        .main__banking-middle-grid-item-2-interact-input input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .main__banking-middle-grid-item-2-interact-input.transfer input {
            padding: 0.5vh;
            margin-top: 0.4vh;
            position: relative;
            z-index: 1;
        }

        .main__banking-middle-grid-item-2-interact-btn,
        .main__banking-middle-grid-item-2-interact-btn-abbrechen {
            margin-top: 0.5vh;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-size: 1.3vh;
            text-transform: uppercase;
            color: var(--color-white);
            border-radius: 0.4vh;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
            padding: 0.75vh;
            cursor: pointer;
            transition: 0.2s ease-in;
        }

        .main__banking-middle-grid-item-2-interact-btn:hover,
        .main__banking-middle-grid-item-2-interact-btn-abbrechen:hover {
            box-shadow: 0vh 0vh 3vh #380303 inset, 0vh 0vh 1.7vh #c11717;
            background: #9c1414;
            border: 0.1vh solid transparent;
        }

        .main__banking-bottom-container {
            margin-top: 2vh;
        }

        .main__banking-bottom-header {
            display: flex;
            width: 100%;
            align-items: center;
            gap: 2vh;
            white-space: nowrap;
            color: var(--color-white);
            font-size: 1.4vh;
        }

        .main__banking-bottom-header-border {
            width: 100%;
            height: 0.1vh;
            background: linear-gradient(270deg, rgba(255, 255, 255, 0) 3.25%, rgba(255, 255, 255, 0.15) 94.81%);
        }

        .main__banking-bottom-scroll-container {
            margin-top: 1vh;
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            gap: 1vh;
            height: 20.5vh;
            overflow-y: scroll;
        }

        .main__banking-bottom-scroll-container::-webkit-scrollbar {
            width: 0.3vh;
            background: var(--clr-new-2-orange);
            border-radius: 5vh;
        }

        .main__banking-bottom-scroll-container::-webkit-scrollbar-thumb {
            background: #ff0000;
            border-radius: 5vh;
            width: 0.3vh;
        }

        .main__banking-bottom-scroll-item {
            position: relative;
            width: 99%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 0.1vh solid rgba(255, 255, 255, 0.15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
            padding: 1vh;
            border-radius: 0.4vh;
            overflow: hidden;
            margin-top: 0.5vh;
            margin-bottom: 0.7vh;
        }

        .main__banking-bottom-scroll-sphere {
            position: absolute;
            bottom: -5vh;
            left: -5vh;
            width: 20vh;
            height: 20vh;
            border-radius: 50%;
            background: #c11717;
            filter: blur(10vh);
            z-index: -1;
        }

        .main__banking-bottom-scroll-sphere.withdraw {
            background: #d93939;
        }

        .main__banking-bottom-scroll-item-info {
            width: 16vh;
        }

        .main__banking-bottom-scroll-item-info p {
            font-size: 1.2vh;
            color: var(--color-white);
        }

        .main__banking-bottom-scroll-item-info p:nth-last-child(1) {
            color: rgba(255, 255, 255, 0.5);
        }

        .main__banking-bottom-scroll-item-info.center {
            text-align: center;
        }

        .main__banking-bottom-scroll-item-info.right {
            text-align: right;
        }

        .main__banking-bottom-scroll-item-info.right p {
            color: #ff4141;
            text-shadow: 0vh 0vh 1.7vh #ff4141;
        }

        .main__banking-bottom-scroll-item-info.right p:nth-last-child(1) {
            color: rgba(255, 255, 255, 0.5);
            text-shadow: none;
        }

        .main__banking-bottom-scroll-item-border {
            position: absolute;
            bottom: 0vh;
            left: 0vh;
            width: 100%;
            height: 0.1vh;
            background: linear-gradient(to right, rgba(0, 0, 0, 0), #d93939, rgba(0, 0, 0, 0));
        }

        .banking-slide-in-bottom {
            animation: banking-slideInBottom 0.3s ease-out forwards;
            display: block !important;
        }
        
        @keyframes banking-slideInBottom {
            0% {
                opacity: 0;
                transform: translateY(50px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .main__lifeinvader-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80vh;
            padding: 3vh;
            border-radius: var(--border-radius-frame);
            background: url(img/main_bg/bg_3.png);
            background-size: cover;
            background-position: center;
            overflow: hidden;
        }
        
        .main__lifeinvader-grid {
            width: 100%;
            display: grid;
            grid-template-columns: 1fr .5fr;
            gap: 1.5vh;
            margin-top: 2vh;
        }
        
        .main__lifeinvader-grid-middle-item {
            position: relative;
            width: 100%;
            height: 40.25vh;
            padding: 1vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            overflow: hidden;
        }
        
        .main__lifeinvader-grid-middle-item-header {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            margin-top: 3vh;
        }
        
        .main__lifeinvader-grid-middle-item-header-text {
            font-family: var(--ff-arame);
            font-size: 3vh;
            font-weight: bold;
            color: #ededed;
            text-shadow: 0vh 0vh 1.7vh #ededed;
            text-transform: uppercase;
        }
        
        .main__lifeinvader-grid-middle-item-header p:nth-last-child(1) {
            font-family: var(--ff-header);
            font-size: 1.2vh;
            font-weight: 400;
            color: rgba(255, 255, 255, .5) !important;
            width: 75%;
        }
        
        .main__lifeinvader-grid-item-middle-bg {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            height: 13vh;
            border-radius: 100%;
            background: rgba(255, 255, 255, .15);
            filter: blur(8.3vh);
            top: -10vh;
            z-index: -1;
        }
        
        .main__lifeinvader-grid-item-middle-bottom-bg {
            position: absolute;
            width: 100%;
            height: 45vh;
            border-radius: 100%;
            left: 50%;
            transform: translateX(-50%);
            bottom: -30.5vh;
            background: rgba(255, 255, 255, .25);
            filter: blur(32.5vh);
            z-index: -1;
        }
        
        .main__lifeinvader-grid-item-middle-text-wrapper {
            position: absolute;
            bottom: 1vh;
            right: 1vh;
            display: flex;
            align-items: center;
            gap: .5vh;
        }
        
        .main__lifeinvader-grid-item-middle-text-count {
            font-size: 1.2vh;
            font-weight: 400;
            color: var(--color-white);
            padding: .5vh;
            padding-left: 1vh;
            padding-right: 1vh;
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
        }
        
        .main__lifeinvader-grid-item-middle-text-count.green p span {
            color: #17c164;
            text-shadow: 0vh 0vh 1.7vh #17c164;
        }
        
        .main__lifeinvader-grid-item-middle-textarea textarea {
            width: 100%;
            height: 48vh;
            background: none;
            outline: none;
            margin-top: 2vh;
            resize: none;
            color: rgba(255, 255, 255, .5);
            border: none;
            font-family: var(--ff-header);
        }
        
        .main__lifeinvader-grid-middle-item-btn-container {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1vh;
            margin-top: 1vh;
        }
        
        .main__lifeinvader-grid-middle-item-btn-left {
            width: 100%;
            padding: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--color-white);
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__lifeinvader-grid-middle-item-btn-left:hover {
            box-shadow: 0vh 0vh 3vh #959595 inset, 0vh 0vh 1.7vh #959595;
            background: #9d9d9d;
            border: .1vh solid transparent;
        }
        
        .main__lifeinvader-grid-middle-item-btn-right {
            width: 100%;
            padding: 1vh;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-size: 1.2vh;
            text-transform: uppercase;
            color: var(--color-white);
            border-radius: .2vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
            cursor: pointer;
            transition: .2s ease-in;
        }
        
        .main__lifeinvader-grid-middle-item-btn-right:hover {
            box-shadow: 0vh 0vh 3vh #631a1a inset, 0vh 0vh 1.7vh #D93939;
            background: #991f1f;
            border: .1vh solid transparent;
        }
        
        .main__lifeinvader-grid-right-item-container {
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 45vh;
            gap: .5vh;
            overflow-y: scroll;
            padding-right: 1vh;
        }
        
        .main__lifeinvader-grid-right-item-container::-webkit-scrollbar {
            width: .3vh;
            background: #fc6e6eaf;
            border-radius: 5vh;
        }
        
        .main__lifeinvader-grid-right-item-container::-webkit-scrollbar-thumb {
            width: .3vh;
            background: #ff2323;
            border-radius: 5vh;
        }
        
        .main__lifeinvader-grid-right-item {
            position: relative;
            width: 95%;
            padding: 1vh;
            overflow: hidden;
            gap: .5vh;
            border-radius: .4vh;
            border: .1vh solid rgba(255, 255, 255, .15);
            box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, .15) inset;
        }
        
        .main__lifeinvader-grid-right-item-right-header {
            font-size: 1.3vh;
            color: var(--color-white);
        }
        
        .main__lifeinvader-grid-right-item-right-text {
            font-size: 1.2vh;
            color: rgba(255, 255, 255, .5);
            padding-top: .25vh;
        }
        
        .main__lifeinvader-grid-right-item-right-text p {
            word-break: break-all;
        }
        
        .main__lifeinvader-grid-right-item-right-call {
            display: flex;
            align-items: center;
            gap: .5vh;
            margin-top: .5vh;
        }
        
        .main__lifeinvader-grid-right-item-right-call-icon {
            width: 2.5vh;
            height: 2.5vh;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: .3vh;
            background: radial-gradient(96% 96% at 50% 50%, rgba(217, 57, 57, 0.3) 0%, rgba(217, 57, 57, 0) 100%);
            color: #ededed;
            font-size: 1.2vh;
        }
        
        .main__lifeinvader-grid-right-item-right-call-text {
            height: 2.5vh;
            font-size: 1.2vh;
            color: var(--color-white);
            border-radius: .3vh;
            background: radial-gradient(1278.19% 193.79% at 0% 6%, rgba(255, 255, 255, 0.3) 0%, rgba(57, 113, 217, 0.00) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 0vh;
            padding-bottom: 0vh;
            padding-left: 1vh;
            padding-right: 1vh;
        }
        
        .main__lifeinvader-grid-right-item-right-call-text p {
            margin-top: -.25vh;
        }
        
        .main__lifeinvader-grid-right-item-left-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .main__lifeinvader-grid-right-item-left-container img {
            width: 5vh;
        }
    </style>
    <main>
         <!-- FINAL CITY HUD 80% -->
        <div class="main__hud-container" style="display: hud;">
            <div class="main__hud-top-container-information">
                <p><span>FINAL</span>U21</p>
            </div>

            <div class="main__hud-top-header-stars" style="display: none;">
                <i class="fas fa-star"></i>
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
            </div>

            <div class="main__hud-top-big-grid">
                <div class="right__id-container">
                    <i class="fa-light fa-hashtag"></i>
                    <span id="playerId">1</span>
                </div>
                <div class="right__speech-container">
                    <div class="main__hud-right-mic">
                        <i class="fa-sharp fa-solid fa-microphone mic" id="microphonestuff"></i>
                        <div class="main__top-right-speech-container">
                            <div class="dot active" id="dot-1"></div>
                            <div class="dot active" id="dot-2"></div>
                            <div class="dot" id="dot-3"></div>
                            <div class="dot" id="dot-4"></div>
                        </div>
                    </div>

                    <div class="main__hud-right-radio">
                        <i class="fa-sharp fa-solid fa-walkie-talkie" id="radioTower"></i>
                        <div class="main__hud-right-radio-activ">
                            <div class="radio" id="radio-1"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main__hud-top-money-container">
                <div class="main__hud-top-money-grid">
                    <div class="main__money-container">
                        <p id="money">390.000.000$</p>
                    </div>
                    <div class="main__money-icon-container">
                        <i class="fa-solid fa-wallet"></i>
                    </div>
                </div>
            </div>

            <div class="main__hud-top-money-container bank__money" id="bankMoneyDisplay">
                <div class="main__hud-top-money-grid">
                    <div class="main__money-container bank__money">
                        <p id="bankhud">390.000.000$</p>
                    </div>
                    <div class="main__money-icon-container bank__money">
                        <i class="fa-sharp fa-solid fa-building-columns"></i>
                    </div>
                </div>
            </div>

            <div class="main__hud-top-money-container black__money" style="display: none;" id="blackMoneyDisplay">
                <div class="main__hud-top-money-grid">
                    <div class="main__money-container black__money">
                        <p id="blackMoney">390.000.000$</p>
                    </div>
                    <div class="main__money-icon-container black__money">
                        <i class="fa-sharp fa-solid fa-building-columns"></i>
                    </div>
                </div>
            </div>

            <div class="main__hud-top-ammonation-container" style="display: none;">
                <div class="main__hud-top-ammonation-weapon-name">
                    <p id="weaponHudLabel">Pistol</p>
                </div>
                <div class="main__hud-top-ammonation-weapon-ammo-count">
                    <p class="main__hud-top-ammonation-weapon-ammo-atm" id="currentAmmo">32/</p>
                    <p class="main__hud-top-ammonation-weapon-ammo-magazine" id="maxAmmo">128</p>
                </div>
                <div class="main__hud-top-ammonation-img">
                    <img src="img/hud/bullet.svg" alt="">
                </div>
            </div>

            <div class="main__hud-job-container react-draggable" style="transform: translate(0px, 0px);">
                <div class="main__hud-bottom-information-item">
                    <div class="main__hud-bottom-information-item-icon">
                        <i class="fa-solid fa-user" aria-hidden="true"></i>
                    </div>
                    <div class="main__hud-bottom-information-item-text">
                        <p id="joblable">Arbeitslos</p>
                        <p id="jobrang">Sozialhilfe</p>
                    </div>
                </div>
            </div>

            <div class="main__hud-location-container react-draggable" style="transform: translate(0px, 0px);">
                <div class="main__hud-bottom-information-item">
                    <div class="main__hud-bottom-information-item-icon">
                        <i class="fa-solid fa-location-dot" aria-hidden="true"></i>
                    </div>
                    <div class="main__hud-bottom-information-item-text">
                        <p id="streetA">Unbekannt</p>
                        <p id="streetB">Unbekannt 1000</p>
                    </div>
                </div>
            </div>

            <div class="main__hud-clock-container">
                <div class="main__hud-clock-item">
                    <i class="fa-solid fa-clock"></i>
                    <div class="main__hud-clock-time">
                        <p id="hours">18:55</p>
                    </div>
                </div>
                <div class="main__hud-clock-item">
                    <i class="fa-solid fa-calendar-week"></i>
                    <div class="main__hud-clock-date">
                        <p id="date">01.04.2022</p>
                    </div>
                </div>
            </div>

            <div class="main__hud-teamstats-container" style="display: none;">
                <div class="main__hud-teamstats-item">
                    <i class="fa-thin fa-plane-departure"></i>
                    <div class="main__hud-teamstats-einreise">
                        <p id="newusercount">14 Einreise</p>
                    </div>
                </div>
                <div class="main__hud-teamstats-item">
                    <i class="fa-thin fa-user-shield"></i>
                    <div class="main__hud-teamstats-guids">
                        <p id="activeguide">5 Guides</p>
                    </div>
                </div>
            </div>

            <div class="main__hud-press-e-container-wrapper" style="display: none;">
                <div class="main__hud-press-e-container">
                    <div class="main__hud-press-e-circle">
                        <div class="main__hud-press-e-circle-2">
                            <p id="interactkey">E</p>
                        </div>
                    </div>
                </div>
                <div class="main__hud-press-text">
                    <p>Drücke E um auf Garage zuzugreifen</p>
                </div>
            </div>

            <div class="main__hud-deathtimeout-container">
                <div class="main__hud-deathtimeout-death-timeout">
                    <span class="main__hud-deathtimeout-text" id="death-timeout-text">Kampffähig In:</span>
                    <span class="main__hud-deathtimeout-time" id="death-timeout-value">05:12</span>
                </div>
            </div>

            <div class="main__notify-container"></div>
            <div class="main__lifeinvader-notify-container"></div>
            <div class="main__teamchat-container"></div>
            <div class="main__hud-announce-container"></div>

            <div class="main__food-drink-container">
                <div class="main__food-container">
                    <div class="main__food-container-grid">
                        <div class="main__food-container-stripes">
                            <div class="main__food-stripes active" id="food-progress-1"></div>
                            <div class="main__food-stripes active" id="food-progress-2"></div>
                            <div class="main__food-stripes" id="food-progress-3"></div>
                            <div class="main__food-stripes" id="food-progress-4"></div>
                        </div>
                    </div>
                    <div class="main__food-drink-text food-text">
                        <p class="main__food-header">Hunger</p>
                        <p class="main__food-text" id="foodtext">80%</p>
                    </div>
                </div>
                <div class="main__drink-container">
                    <div class="main__drink-container-grid">
                        <div class="main__drink-container-stripes">
                            <div class="main__drink-stripes active" id="drink-progress-1"></div>
                            <div class="main__drink-stripes active" id="drink-progress-2"></div>
                            <div class="main__drink-stripes active" id="drink-progress-3"></div>
                            <div class="main__drink-stripes" id="drink-progress-4"></div>
                        </div>
                    </div>
                    <div class="main__food-drink-text drink-text">
                        <p class="main__drink-header">Durst</p>
                        <p class="main__drink-text" id="drinktext">100%</p>
                    </div>
                </div>
            </div>
            

            <div class="main__hud-mute-container" style="display: none;">
                <div class="main__hud-mute-box">
                    <div class="main__hud-mute-outer-circle"></div>
                    <div class="main__hud-mute-inner-circle"></div>
                    <div class="main__hud-mute-img">
                        <img src="img/hud/mute.png" alt="">
                    </div>
                </div>
            </div>

            <div class="main__hud-top-chat-container" style="display: none;">
                <div class="main__hud-top-chat-input-container">
                    <input type="text" id="chatRes" placeholder="Write a message here...">
                    <div class="main__hud-top-chat-input-img">
                        <img src="img/hud/chat.png" alt="">
                    </div>
                </div>
            </div>
        </div>

        <div class="main__hud-carhud-container" style="transform: translateY(0vh) scale(1) translateZ(0px); display: none;">
            <div class="react-draggable" style="transform: translate(0px, 0px);">
                <div class="main__hud-carhud-flex">
                    <div class="main__hud-carhud-flex-top">
                        <div class="main__hud-carhud-flex-top-left">
                            <p id="carSpeed">105 <span>km/h</span></p>
                        </div>
                        <div class="main__hud-carhud-flex-top-right">
                            <div class="main__hud-carhud-flex-top-right-gear">
                                <div class="main__hud-carhud-flex-top-right-gear-icon">
                                    <i class="fa-sharp fa-solid fa-gear"aria-hidden="true"></i></div>
                                    <div class="main__hud-carhud-flex-top-right-gear-text">
                                        <p id="cargear">1</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="main__hud-carhud-fuel-container">
                            <div class="main__hud-carhud-fuel-progress">
                                <div class="main__hud-carhud-fuel-progress-fill"></div>
                            </div>
                        </div>
                        <div class="main__hud-carhud-flex-bottom">
                            <div class="main__hud-carhud-flex-bottom-item active">
                                <i class="fa-sharp fa-solid fa-lock-keyhole"aria-hidden="true"></i>
                                <div class="main__hud-carhud-flex-bottom-item-stripe" id="carLock"></div>
                            </div>
                            <div class="main__hud-carhud-flex-bottom-item active">
                                <i class="fa-sharp fa-solid fa-engine"aria-hidden="true"></i>
                                <div class="main__hud-carhud-flex-bottom-item-stripe" id="carEngine"></div>
                            </div>
                            <div class="main__hud-carhud-flex-bottom-item active">
                                <i class="fa-sharp fa-solid fa-lightbulb-gear"aria-hidden="true"></i>
                                <div class="main__hud-carhud-flex-bottom-item-stripe active"></div>
                            </div>
                            <div class="main__hud-carhud-fuel-icon"><i class="fa-sharp fa-solid fa-gas-pump" aria-hidden="true"></i>
                                <p id="carFuel">65L</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- FINAL CITY HUD 100% -->