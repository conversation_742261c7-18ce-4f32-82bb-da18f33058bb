class FinalCityHUD {
    constructor() {
        this.hudVisible = true;
        this.inVehicle = false;
        this.isDragging = false;
        this.dragElement = null;
        this.dragOffset = { x: 0, y: 0 };
        this.hasWeapon = false;
        this.settings = {
            hudVisible: true,
            job: true,
            location: true,
            money: true,
            positions: {
                job: { x: 0, y: 0 },
                location: { x: 0, y: 0 },
                hunger: { x: 0, y: 0 },
                thirst: { x: 0, y: 0 },
                weapon: { x: 0, y: 0 }
            }
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.startTimeUpdate();
        this.loadSettings();

        fetch(`https://${GetParentResourceName()}/uiLoaded`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
        });
    }

    bindEvents() {
        window.addEventListener('message', this.onMessage.bind(this));
        
        document.querySelector('.main__corleone-hud-settings-btn-save').addEventListener('click', this.saveSettings.bind(this));
        document.querySelector('.main__corleone-hud-settings-btn-reset').addEventListener('click', this.resetSettings.bind(this));
        document.querySelector('.main__corleone-hud-settings-close-btn').addEventListener('click', this.closeSettings.bind(this));
        
        document.querySelectorAll('.main__corleone-hud-settings-right-checkbox').forEach(checkbox => {
            checkbox.addEventListener('click', this.toggleSetting.bind(this));
        });
        
        const chatInput = document.querySelector('.main__final-chat-input input');
        const chatIcon = document.querySelector('.main__final-chat-icon');
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && document.activeElement === chatInput) {
                this.deactivateChat();
            }
        });
        
        chatIcon.addEventListener('click', () => {
            this.activateChat();
        });
        
        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (chatInput.value.trim()) {
                    this.sendChatMessage(chatInput.value);
                    chatInput.value = '';
                } else {
                    this.deactivateChat();
                }
            }
            if (e.key === 'Escape') {
                e.preventDefault();
                chatInput.value = '';
                this.deactivateChat();
            }
        });
        
        document.addEventListener('mousedown', this.onMouseDown.bind(this));
        document.addEventListener('mousemove', this.onMouseMove.bind(this));
        document.addEventListener('mouseup', this.onMouseUp.bind(this));

    }

    onMessage(event) {
        const data = event.data;
        
        switch(data.type) {
            case 'updatePlayerData':
                this.updatePlayerData(data.playerData);
                break;
            case 'updateMoney':
                this.updateMoney(data.cash, data.bank);
                break;
            case 'updateJob':
                this.updateJob(data.job, data.grade);
                break;
            case 'updateLocation':
                this.updateLocation(data.street, data.zone);
                break;
            case 'updateStatus':
                this.updateStatus(data.hunger, data.thirst);
                break;
            case 'updateVehicle':
                this.updateVehicleHUD(data);
                break;
            case 'updateVehicleSpeed':
                this.updateVehicleSpeed(data.speed);
                break;
            case 'updateWeapon':
                this.updateWeaponDisplay(data.weaponData);
                break;
            case 'showNotification':
                this.showNotification(data.notification.title, data.notification.message, data.notification.type, data.notification.duration);
                break;
            case 'showProgress':
                this.showProgress(data.text, data.duration);
                break;
            case 'hideProgress':
                this.hideProgress();
                break;
            case 'showInteraction':
                this.showInteraction(data.text);
                break;
            case 'hideInteraction':
                this.hideInteraction();
                break;
            case 'openSettings':
                this.openSettings();
                break;
            case 'toggleHud':
                this.toggleHUD(data.show);
                break;
            case 'setVehicleState':
                this.setVehicleState(data.inVehicle);
                break;
            case 'showAnnouncement':
                this.showAnnouncement(data.title, data.message, data.duration);
                break;
            case 'closeSettings':
                this.closeSettings();
                break;
            case 'openChat':
                this.activateChat();
                break;
            case 'closeChat':
                this.deactivateChat();
                break;
            case 'addChatMessage':
                this.addChatMessage(data.template, data.args);
                break;
            case 'updatePlayerCount':
                this.updatePlayerCount(data.current, data.max);
                break;
            case 'startProgressbar':
                this.showProgress(data.name, data.duration);
                break;
            case 'stopProgressbar':
                this.hideProgress();
                break;
        }
        
        if (data.action) {
            switch(data.action) {
                case 'voiceStatus':
                    this.updateVoiceStatus(data.status, data.muted);
                    break;
                case 'SetVoiceRange':
                    this.updateVoiceRange(data.range);
                    break;
                case 'funk':
                    this.updateRadioStatus(data.funk);
                    break;
            }
        }
    }

    updatePlayerData(data) {
        if (!data) return;
        
        const playerIdElement = document.querySelector('.main__corleone-hud-top-right-information-top-flex-item-id p');
        if (playerIdElement && data.id !== undefined) {
            playerIdElement.textContent = data.id || 'N/A';
        }
        

        const moneyElement = document.querySelector('.main__final-hud-top-right-information-money p');
        if (moneyElement && data.money !== undefined) {
            moneyElement.textContent = `${this.formatMoney(data.money || 0)}€`;
        }
        
        const bankMoneyElement = document.querySelector('.main__final-hud-top-right-information-black-money p');
        if (bankMoneyElement && data.bankMoney !== undefined) {
            bankMoneyElement.textContent = `${this.formatMoney(data.bankMoney || 0)}€`;
        }
        

        const jobElement = document.querySelector('.main__final-hud-bottom-information-item-text p:first-child');
        if (jobElement && data.job !== undefined) {
            jobElement.textContent = data.job || 'Arbeitslos';
        }
        
        const jobGradeElement = document.querySelector('.main__final-hud-bottom-information-item-text p:last-child');
        if (jobGradeElement && data.jobGrade !== undefined) {
            jobGradeElement.textContent = data.jobGrade || 'Ohne Rang';
        }
    }

    updateMoney(cash, bank) {
        const moneyElement = document.querySelector('.main__final-hud-top-right-information-money p');
        if (moneyElement && cash !== undefined) {
            moneyElement.textContent = `${this.formatMoney(cash || 0)}€`;
        }
        
        const bankMoneyElement = document.querySelector('.main__final-hud-top-right-information-black-money p');
        if (bankMoneyElement && bank !== undefined) {
            bankMoneyElement.textContent = `${this.formatMoney(bank || 0)}€`;
        }
    }

    updateJob(job, grade) {
        const jobContainer = document.querySelector('.main__final-hud-job-container .main__final-hud-bottom-information-item-text');
        if (jobContainer) {
            const jobElement = jobContainer.querySelector('p:first-child');
            if (jobElement) {
                jobElement.textContent = job || 'Arbeitslos';
            }
            
            const gradeElement = jobContainer.querySelector('p:last-child');
            if (gradeElement) {
                gradeElement.textContent = grade || 'Ohne Rang';
            }
        }
    }

    updateLocation(street, zone) {
        const streetAElement = document.getElementById('streetA');
        if (streetAElement) {
            streetAElement.textContent = street || 'Unbekannt';
        }
        
        const streetBElement = document.getElementById('streetB');
        if (streetBElement) {
            streetBElement.textContent = zone || 'Unbekannt';
        }
    }

    updateStatus(hunger, thirst) {

        const hungerElement = document.querySelector('#hungerPercent p');
        if (hungerElement && hunger !== undefined) {
            hungerElement.textContent = `${hunger || 0}%`;
            this.updateFoodBar(hunger || 0, 'hunger');
        }
        

        const thirstElement = document.querySelector('#thirstPercent p');
        if (thirstElement && thirst !== undefined) {
            thirstElement.textContent = `${thirst || 0}%`;
            this.updateFoodBar(thirst || 0, 'thirst');
        }
    }

    updateFoodBar(percent, type) {
        if (percent === undefined || percent === null || isNaN(percent)) {
            percent = 0;
        }
        
        const container = type === 'thirst' ? 
        document.querySelector('#thirstPercent').parentElement :
        document.querySelector('#hungerPercent').parentElement;
        
        const items = container.querySelectorAll('.main__corleone-hud-bottom-information-food-5-item-percent-item');
        const activeClass = type === 'thirst' ? 'active_drink5' : 'active_food5';
        
        items.forEach((item, index) => {
            if (index < Math.floor(percent / 20)) {
                item.classList.add(activeClass);
                item.classList.remove('false');
            } else {
                item.classList.remove(activeClass);
                item.classList.add('false');
            }
        });
    }

    updateWeaponDisplay(weaponData) {
        const weaponContainer = document.getElementById('weaponContainer');
        const weaponIcon = document.getElementById('weaponIcon');
        const weaponAmmo = document.getElementById('weaponAmmo');
        
        if (!weaponContainer || !weaponIcon || !weaponAmmo) {
            return;
        }
        
        if (!weaponData || !weaponData.hasWeapon) {
            this.hasWeapon = false;
            weaponContainer.style.display = 'none';
            return;
        }
        
        this.hasWeapon = true;
        weaponContainer.style.display = 'block';
        
        const currentAmmo = Math.max(0, parseInt(weaponData.currentAmmo) || 0);
        const reserveAmmo = Math.max(0, parseInt(weaponData.reserveAmmo) || 0);
        const maxClipSize = Math.max(1, parseInt(weaponData.maxClipSize) || 1);
        
        const weaponIcons = {
            'pistol': 'fa-gun',
            'rifle': 'fa-gun',
            'shotgun': 'fa-gun',
            'sniper': 'fa-crosshairs',
            'melee': 'fa-sword',
            'grenade': 'fa-bomb',
            'default': 'fa-gun'
        };
        
        const iconClass = weaponIcons[weaponData.type] || weaponIcons.default;
        weaponIcon.className = `fa-sharp fa-solid ${iconClass}`;
        
        const ammoText = weaponAmmo.querySelector('p');
        if (ammoText) {
            ammoText.textContent = `${currentAmmo}/${reserveAmmo}`;

        }
        
        const ammoBars = weaponContainer.querySelectorAll('.main__corleone-hud-bottom-information-food-5-item-percent-item');
        if (ammoBars.length > 0) {

            const ammoPercentage = currentAmmo / maxClipSize;
            const ammoLevel = Math.max(0, Math.ceil(ammoPercentage * 5));
            

            
            ammoBars.forEach((bar, index) => {

                bar.classList.remove('active_ammo', 'active_food5', 'active_drink5', 'false');
                
                if (index < ammoLevel) {
                    bar.classList.add('active_ammo');
                } else {
                    bar.classList.add('false');
                }
            });
        }
    }

    updateVehicleHUD(data) {
        if (!this.inVehicle || !data) return;

        const gearElement = document.querySelector('.main__final-carhud-flex-top-right-gear-text p');
        if (gearElement && data.gear !== undefined) {
            gearElement.textContent = data.gear || 'N';
        }
        

        const fuelFillElement = document.querySelector('.main__final-carhud-fuel-progress-fill');
        const fuelIconElement = document.querySelector('.main__final-carhud-fuel-icon p');
        if (fuelFillElement && data.fuel !== undefined) {
            fuelFillElement.style.width = `${Math.max(0, data.fuel || 0)}%`;
        }
        if (fuelIconElement && data.fuel !== undefined) {
            fuelIconElement.textContent = `${Math.round(data.fuel || 0)}L`;
        }
        

        const statusItems = document.querySelectorAll('.main__final-carhud-flex-bottom-item');
        if (statusItems.length >= 3) {
            statusItems[0].classList.toggle('active', data.locked === true);
            statusItems[1].classList.toggle('active', data.engine === true);
            statusItems[2].classList.toggle('active', data.lights === true);
        }
    }

    updateVehicleSpeed(speed) {
        if (!this.inVehicle) return;
        
        const speedElement = document.querySelector('.main__final-carhud-flex-top-left p');
        if (speedElement && speed !== undefined) {
            speedElement.innerHTML = `${Math.round(speed || 0)} <span>km/h</span>`;
        }
    }

    setVehicleState(inVehicle) {
        this.inVehicle = inVehicle;
        this.updateVehicleHUDVisibility();
        this.updateVehicleSpeed(0);
    }
    
    updateVehicleHUDVisibility() {
        const vehicleHUD = document.querySelector('.main__final-carhud-container');
        if (!vehicleHUD) return;
        
        if (this.inVehicle) {
            vehicleHUD.style.display = 'block';
            return;
        }
        
        const settingsContainer = document.querySelector('.main__corleone-hud-settings-container');
        const settingsOpen = settingsContainer && (settingsContainer.style.display === 'block' || settingsContainer.style.opacity === '1');
        const isDraggingVehicleHUD = vehicleHUD.classList.contains('dragging') || this.dragElement === vehicleHUD;
        
        if (!this.isDragging && !settingsOpen && !isDraggingVehicleHUD) {
            vehicleHUD.style.display = 'none';
        }
    }

    showNotification(title, message, type = 'info', duration = 5000) {
        const container = document.querySelector('.main__final-hud-notify-container');
        const notification = document.createElement('div');
        notification.className = 'main__final-hud-notify-item-bg notify';
        notification.style.transform = 'translateX(0vh) translateZ(0px)';
        

        let iconClass;
        let notificationTitle = title;
        
        switch(type) {
            case 'success':
                iconClass = 'fa-circle-check';
                if (!notificationTitle) notificationTitle = 'Erfolg';
                break;
            case 'error':
                iconClass = 'fa-circle-xmark';
                if (!notificationTitle) notificationTitle = 'Fehler';
                break;
            case 'warning':
                iconClass = 'fa-triangle-exclamation';
                if (!notificationTitle) notificationTitle = 'Warnung';
                break;
            case 'help':
                iconClass = 'fa-circle-question';
                if (!notificationTitle) notificationTitle = 'Hilfe';
                break;
            default:
                iconClass = 'fa-circle-exclamation';
                if (!notificationTitle) notificationTitle = 'Information';
        }
        
        notification.innerHTML = `
            <div class="main__final-hud-notify-item">
                <div class="main__final-hud-notify-item-flex announce">
                    <div class="main__final-hud-notify-item-icon ${type}">
                        <i class="fa-sharp fa-solid ${iconClass}" aria-hidden="true"></i>
                    </div>
                    <div class="main__final-hud-notify-item-info">
                        <p>${notificationTitle}</p>
                        <p>${message}</p>
                    </div>
                </div>
                <div class="main__final-hud-notify-stripe ${type}"></div>
                <div class="main__final-hud-notify-progress">
                    <div class="main__final-hud-notify-progress-fill ${type}" style="animation: ${duration}ms linear 0s 1 normal none running hud-progress;"></div>
                </div>
            </div>
        `;
        
        container.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    }

    showProgress(text, duration) {
        const container = document.querySelector('.main__v3-hud-progess-container');
        const textElement = document.querySelector('.main__v3-hud-progress-top-information-container p:first-child');
        const percentElement = document.querySelector('.main__v3-hud-progress-top-information-container p:last-child');
        
        textElement.textContent = text;
        container.style.display = 'block';
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += (100 / duration) * 100;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                this.hideProgress();
            }
            percentElement.textContent = `${Math.round(progress)}%`;
            this.updateProgressBar(progress);
        }, 100);
    }

    updateProgressBar(progress) {
        const items = document.querySelectorAll('.main__v3-hud-progress-bottom-loading-item');
        const activeItems = Math.floor((progress / 100) * items.length);
        
        items.forEach((item, index) => {
            if (index < activeItems) {
                item.classList.add('active');
                item.classList.remove('false');
            } else {
                item.classList.remove('active');
                item.classList.add('false');
            }
        });
    }

    hideProgress() {
        document.querySelector('.main__v3-hud-progess-container').style.display = 'none';
    }

    showInteraction(text) {
        const container = document.querySelector('.main__v3-hud-interaction-container');
        const textElement = document.querySelector('.main__v3-hud-interaction-information-container p');
        
        textElement.textContent = text;
        container.style.display = 'block';
        container.style.transform = 'translateX(-50%) translateY(0vh) scale(1) translateZ(0px)';
    }

    hideInteraction() {
        const container = document.querySelector('.main__v3-hud-interaction-container');
        container.style.display = 'none';
        container.style.transform = 'translateX(-50%) translateY(0vh) scale(0) translateZ(0px)';
    }

    toggleHUD(show) {
        if (show !== undefined) {
            this.hudVisible = show;
            this.settings.hudVisible = show;
        } else {
            this.hudVisible = !this.hudVisible;
            this.settings.hudVisible = this.hudVisible;
        }
        document.querySelector('.main__corleone-hud-container').style.display = this.hudVisible ? 'block' : 'none';
    }

    openSettings() {
        const container = document.querySelector('.main__corleone-hud-settings-container');
        container.style.display = 'block';
        container.style.opacity = '1';
        container.style.transform = 'translateX(-50%) translateY(-50%) scale(1) translateZ(0px)';
        
        const weaponContainer = document.getElementById('weaponContainer');
        if (weaponContainer) {
            weaponContainer.style.display = 'block';
        }
        
        this.enableDragMode();
        
        fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ hasFocus: true, hasCursor: true })
        });
    }

    closeSettings() {
        const container = document.querySelector('.main__corleone-hud-settings-container');
        container.style.opacity = '0';
        container.style.transform = 'translateX(-50%) translateY(-50%) scale(0) translateZ(0px)';
        setTimeout(() => {
            container.style.display = 'none';
        }, 300);
        
        this.updateVehicleHUDVisibility();
        
        const weaponContainer = document.getElementById('weaponContainer');
        if (weaponContainer && !this.hasWeapon) {
            weaponContainer.style.display = 'none';
        }
        
        this.disableDragMode();
        
        fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ hasFocus: false, hasCursor: false })
        });
        
        fetch(`https://${GetParentResourceName()}/closeSettings`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }

    toggleSetting(event) {
        const checkbox = event.currentTarget;
        const setting = checkbox.dataset.setting;
        const inner = checkbox.querySelector('.main__corleone-hud-settings-right-checkbox-inner');
        
        if (inner.querySelector('i')) {
            inner.innerHTML = '';
            this.settings[setting] = false;
        } else {
            inner.innerHTML = '<i class="fa-regular fa-check" aria-hidden="true"></i>';
            this.settings[setting] = true;
        }
        
        this.applySettings();
    }

    saveSettings() {
        localStorage.setItem('hudSettings', JSON.stringify(this.settings));
        this.applySettings();
        this.applyPositions();
        
        this.updateVehicleHUDVisibility();
        
        const weaponContainer = document.getElementById('weaponContainer');
        if (weaponContainer && !this.hasWeapon) {
            weaponContainer.style.display = 'none';
        }
        
        fetch(`https://${GetParentResourceName()}/saveHudSettings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(this.settings)
        });
        
        fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ hasFocus: false, hasCursor: false })
        });
        
        this.closeSettings();
    }

    resetSettings() {
        this.settings = {
            hudVisible: true,
            job: true,
            location: true,
            money: true,
            positions: {
                job: { x: 0, y: 0 },
                location: { x: 0, y: 0 },
                hunger: { x: 0, y: 0 },
                thirst: { x: 0, y: 0 },
                weapon: { x: 0, y: 0 }
            }
        };
        const elements = {
            '.main__final-hud-job-container': { transform: 'translate(0px, 0px)' },
            '.main__final-hud-location-container': { transform: 'translate(0px, 0px)' },
            '.main__final-food-container .react-draggable:first-child': { transform: 'translate(0px, 0px)' },
            '.main__final-food-container .react-draggable:last-child': { transform: 'translate(0px, 0px)' },
            '.main__final-weapon-container .react-draggable': { transform: 'translate(0px, 0px)' }
        };
        
        Object.keys(elements).forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                element.style.transform = elements[selector].transform;
            }
        });
        
        document.querySelectorAll('.main__corleone-hud-settings-right-checkbox').forEach(checkbox => {
            const setting = checkbox.dataset.setting;
            const inner = checkbox.querySelector('.main__corleone-hud-settings-right-checkbox-inner');
            
            if (this.settings[setting]) {
                inner.innerHTML = '<i class="fa-regular fa-check" aria-hidden="true"></i>';
            } else {
                inner.innerHTML = '';
            }
        });
        
        this.applyPositions();
        this.applySettings();
        fetch(`https://${GetParentResourceName()}/resetHudSettings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
        
        localStorage.setItem('hudSettings', JSON.stringify(this.settings));
    }

    applySettings() {
        document.querySelector('.main__final-hud-job-container').style.display = this.settings.job ? 'block' : 'none';
        document.querySelector('.main__final-hud-location-container').style.display = this.settings.location ? 'block' : 'none';
        document.querySelector('.main__final-hud-top-right-information-container').style.display = this.settings.money ? 'block' : 'none';
        document.querySelector('.main__corleone-hud-container').style.display = this.settings.hudVisible ? 'block' : 'none';
    }

    loadSettings() {
        const savedSettings = localStorage.getItem('hudSettings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
            this.applySettings();
            this.applyPositions();
        }
    }

    startTimeUpdate() {
        const updateTime = () => {
            const now = new Date();
            const time = now.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' });
            const date = now.toLocaleDateString('de-DE');
            
            const timeElement = document.querySelector('.main__final-hud-top-left-information-item:first-child p');
            const dateElement = document.getElementById('dateDisplay');
            
            timeElement.textContent = time;
            dateElement.textContent = date;
        };

        updateTime();
        setInterval(updateTime, 10000);
    }

    formatMoney(amount) {
        if (amount === undefined || amount === null || isNaN(amount)) {
            return '0';
        }
        return new Intl.NumberFormat('de-DE').format(amount);
    }

    updatePlayerCount(current, max) {
        const playerCountElement = document.getElementById('playerCount');
        if (playerCountElement) {
            playerCountElement.textContent = `${current || 0}/${max || 1337}`;
        }
    }

    showAnnouncement(title, message, duration = 10000) {
        const container = document.querySelector('.main__final-hud-announce-container');
        container.style.display = 'block';
        
        const announcement = document.createElement('div');
        announcement.className = 'main__final-hud-notify-item-bg announce';
        announcement.style.transform = 'translateY(0vh) translateZ(0px)';
        
        announcement.innerHTML = `
            <div class="main__final-hud-notify-item">
                <div class="main__final-hud-notify-item-flex announce">
                    <div class="main__final-hud-notify-item-icon"><i class="fa-sharp fa-solid fa-circle-exclamation" aria-hidden="true"></i></div>
                    <div class="main__final-hud-notify-item-info">
                        <p>${title}</p>
                        <p>${message}</p>
                    </div>
                </div>
                <div class="main__final-hud-notify-stripe"></div>
                <div class="main__final-hud-notify-progress">
                    <div class="main__final-hud-notify-progress-fill" style="animation: ${duration}ms linear 0s 1 normal none running hud-progress;"></div>
                </div>
            </div>
        `;
        
        container.appendChild(announcement);
        
        setTimeout(() => {
            if (announcement.parentNode) {
                announcement.parentNode.removeChild(announcement);
            }
            if (container.children.length === 0) {
                container.style.display = 'none';
            }
        }, duration);
    }
    
    activateChat() {
        const chatInput = document.querySelector('.main__final-chat-input input');
        const chatContainer = document.querySelector('.main__final-chat-container');
        
        chatContainer.style.display = 'flex';
        chatContainer.style.opacity = '1';
        chatInput.focus();
        
        fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ hasFocus: true, hasCursor: true })
        });
        
        fetch(`https://${GetParentResourceName()}/updateChatStatus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ active: true })
        });
        
        fetch(`https://${GetParentResourceName()}/chatActive`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ active: true })
        });
    }
    
    deactivateChat() {
        const chatInput = document.querySelector('.main__final-chat-input input');
        const chatContainer = document.querySelector('.main__final-chat-container');
        
        chatInput.blur();
        chatContainer.style.opacity = '0';
        chatContainer.style.display = 'none';
        
        fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ hasFocus: false, hasCursor: false })
        });
        
        fetch(`https://${GetParentResourceName()}/updateChatStatus`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ active: false })
        });
        
        fetch(`https://${GetParentResourceName()}/chatActive`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ active: false })
        });
    }
    
    sendChatMessage(message) {
        if (message.trim() === '') return;
        

        if (message.startsWith('/')) {

            fetch(`https://${GetParentResourceName()}/chatCommand`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ command: message })
            }).then(() => {
                this.deactivateChat();
            });
        } else {

            fetch(`https://${GetParentResourceName()}/chatMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            }).then(() => {
                this.deactivateChat();
            });
        }
    }

    addChatMessage(template, args) {
        const chatMessages = document.querySelector('.main__final-chat-messages');
        if (!chatMessages) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.innerHTML = template.replace(/{(\d+)}/g, (match, index) => {
            return args[parseInt(index)] || '';
        });
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        

        const messages = chatMessages.children;
        if (messages.length > 50) {
            chatMessages.removeChild(messages[0]);
        }
    }


    updateVoiceStatus(isTalking, isMuted) {
        const micElement = document.querySelector('.main__final-hud-top-right-information-top-flex-item:last-child');
        if (!micElement) return;
        

        micElement.classList.remove('speak', 'muted', 'false');
        
        if (isMuted) {
            micElement.classList.add('muted');
        } else if (isTalking) {
            micElement.classList.add('speak');
        } else {
            micElement.classList.add('false');
        }
    }
    
    updateVoiceRange(range) {
        const rangeElement = document.querySelector('.main__final-hud-top-right-information-top-flex-item-range-fill');
        if (!rangeElement) return;
        

        const percentage = (range / 3) * 100;
        rangeElement.style.width = `${percentage}%`;
    }
    
    updateRadioStatus(hasRadio) {
        const radioElement = document.querySelector('.main__final-hud-top-right-information-top-flex-item:nth-last-child(2)');
        if (!radioElement) return;
        

        radioElement.classList.remove('active', 'false');
        
        if (hasRadio) {
            radioElement.classList.add('active');
        } else {
            radioElement.classList.add('false');
        }
    }

    onMouseDown(event) {
        const settingsContainer = document.querySelector('.main__corleone-hud-settings-container');
        if (!settingsContainer || settingsContainer.style.display === 'none') {
            return;
        }

        const draggableElement = event.target.closest('.react-draggable');
        if (!draggableElement) return;

        event.preventDefault();
        this.isDragging = true;
        this.dragElement = draggableElement;
        
        const vehicleHUD = draggableElement.closest('.main__final-carhud-container');
        if (vehicleHUD) {
            vehicleHUD.style.display = 'block';
        }
        
        const rect = draggableElement.getBoundingClientRect();
        this.dragOffset = {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
        
        draggableElement.classList.add('dragging');
        document.body.style.userSelect = 'none';
    }

    onMouseMove(event) {
        if (!this.isDragging || !this.dragElement) return;

        event.preventDefault();
        
        const x = event.clientX - this.dragOffset.x;
        const y = event.clientY - this.dragOffset.y;
        
        this.dragElement.style.position = 'fixed';
        this.dragElement.style.left = x + 'px';
        this.dragElement.style.top = y + 'px';
        this.dragElement.style.transition = 'none';
    }

    onMouseUp(event) {
        if (!this.isDragging || !this.dragElement) return;

        const wasVehicleHUD = this.dragElement.closest('.main__final-carhud-container');
        
        this.isDragging = false;
        this.dragElement.classList.remove('dragging');
        this.dragElement.style.transition = '';
        document.body.style.userSelect = '';
        
        const elementType = this.getElementType(this.dragElement);
        if (elementType) {
            const rect = this.dragElement.getBoundingClientRect();
            this.settings.positions[elementType] = {
                x: rect.left,
                y: rect.top
            };
        }
        
        this.dragElement = null;
        
        if (wasVehicleHUD) {
            setTimeout(() => {
                this.updateVehicleHUDVisibility();
            }, 100);
        }
    }

    getElementType(element) {
        if (element.classList.contains('main__final-hud-job-container')) {
            return 'job';
        } else if (element.classList.contains('main__final-hud-location-container')) {
            return 'location';
        } else if (element.closest('.main__final-food-container')) {
            const icons = element.querySelectorAll('i');
            for (let icon of icons) {
                if (icon.classList.contains('fa-glass-water')) {
                    return 'thirst';
                } else if (icon.classList.contains('fa-utensils')) {
                    return 'hunger';
                }
            }
        } else if (element.closest('.main__final-weapon-container')) {
            return 'weapon';
        }
        return null;
    }

    enableDragMode() {
        document.body.classList.add('drag-mode');
    }

    disableDragMode() {
        document.body.classList.remove('drag-mode');
    }

    applyPositions() {
        Object.keys(this.settings.positions).forEach(elementType => {
            const position = this.settings.positions[elementType];
            let element = null;
            
            switch(elementType) {
                case 'job':
                    element = document.querySelector('.main__final-hud-job-container');
                    break;
                case 'location':
                    element = document.querySelector('.main__final-hud-location-container');
                    break;
                case 'hunger':
                    element = document.querySelector('.main__final-food-container .react-draggable:last-child');
                    break;
                case 'thirst':
                    element = document.querySelector('.main__final-food-container .react-draggable:first-child');
                    break;
                case 'weapon':
                    element = document.querySelector('.main__final-weapon-container .react-draggable');
                    break;
            }
            
            if (element && position.x !== 0 && position.y !== 0) {
                element.style.position = 'fixed';
                element.style.left = position.x + 'px';
                element.style.top = position.y + 'px';
            }
        });
    }

}

document.addEventListener('DOMContentLoaded', () => {
    window.finalCityHUD = new FinalCityHUD();
});