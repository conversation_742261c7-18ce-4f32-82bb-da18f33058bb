-- local variables
local talking = false
local muted = false
local chatActive = false

local currentVehicle = 0

-- local functions / helpers
local function getWeaponAmmo(playerPed, weapon)
    local _, clipAmmoRaw = GetAmmoInClip(playerPed, weapon)
    local totalAmmoRaw = GetPedAmmoByType(playerPed, GetPedAmmoTypeFromWeapon(playerPed, weapon))
    local maxClipSizeRaw = GetMaxAmmoInClip(playerPed, weapon, true)
    
    local clipAmmo = tonumber(clipAmmoRaw) or 0
    local totalAmmo = tonumber(totalAmmoRaw) or 0
    local maxClipSize = tonumber(maxClipSizeRaw) or 1

    clipAmmo = math.max(0, clipAmmo)
    totalAmmo = math.max(0, totalAmmo)
    maxClipSize = math.max(1, maxClipSize)

    local reserveAmmo = math.max(0, totalAmmo - clipAmmo)

    return {
        clipAmmo = clipAmmo,
        reserveAmmo = reserveAmmo,
        maxClipSize = maxClipSize,
    }
end

local function getWeaponType(weapon)
    local weaponTypes = {
        [`WEAPON_PISTOL`] = 'pistol',
        [`WEAPON_PISTOL_MK2`] = 'pistol',
        [`WEAPON_COMBATPISTOL`] = 'pistol',
        [`WEAPON_HEAVYPISTOL`] = 'pistol',
        [`WEAPON_VINTAGEPISTOL`] = 'pistol',
        [`WEAPON_SNSPISTOL`] = 'pistol',
        [`WEAPON_PISTOL50`] = 'pistol',
        [`WEAPON_APPISTOL`] = 'pistol',
        [`WEAPON_STUNGUN`] = 'pistol',
        [`WEAPON_FLAREGUN`] = 'pistol',
        [`WEAPON_MARKSMANPISTOL`] = 'pistol',
        
        [`WEAPON_ASSAULTRIFLE`] = 'rifle',
        [`WEAPON_ASSAULTRIFLE_MK2`] = 'rifle',
        [`WEAPON_CARBINERIFLE`] = 'rifle',
        [`WEAPON_CARBINERIFLE_MK2`] = 'rifle',
        [`WEAPON_ADVANCEDRIFLE`] = 'rifle',
        [`WEAPON_SPECIALCARBINE`] = 'rifle',
        [`WEAPON_SPECIALCARBINE_MK2`] = 'rifle',
        [`WEAPON_BULLPUPRIFLE`] = 'rifle',
        [`WEAPON_BULLPUPRIFLE_MK2`] = 'rifle',
        [`WEAPON_COMPACTRIFLE`] = 'rifle',
        [`WEAPON_MILITARYRIFLE`] = 'rifle',
        [`WEAPON_HEAVYRIFLE`] = 'rifle',
        [`WEAPON_TACTICALRIFLE`] = 'rifle',
        
        [`WEAPON_PUMPSHOTGUN`] = 'shotgun',
        [`WEAPON_PUMPSHOTGUN_MK2`] = 'shotgun',
        [`WEAPON_SAWNOFFSHOTGUN`] = 'shotgun',
        [`WEAPON_ASSAULTSHOTGUN`] = 'shotgun',
        [`WEAPON_BULLPUPSHOTGUN`] = 'shotgun',
        [`WEAPON_MUSKET`] = 'shotgun',
        [`WEAPON_HEAVYSHOTGUN`] = 'shotgun',
        [`WEAPON_DBSHOTGUN`] = 'shotgun',
        [`WEAPON_AUTOSHOTGUN`] = 'shotgun',
        [`WEAPON_COMBATSHOTGUN`] = 'shotgun',
        
        [`WEAPON_SNIPERRIFLE`] = 'sniper',
        [`WEAPON_HEAVYSNIPER`] = 'sniper',
        [`WEAPON_HEAVYSNIPER_MK2`] = 'sniper',
        [`WEAPON_MARKSMANRIFLE`] = 'sniper',
        [`WEAPON_MARKSMANRIFLE_MK2`] = 'sniper',
        [`WEAPON_PRECISIONRIFLE`] = 'sniper',
        
        [`WEAPON_GRENADE`] = 'grenade',
        [`WEAPON_BZGAS`] = 'grenade',
        [`WEAPON_MOLOTOV`] = 'grenade',
        [`WEAPON_STICKYBOMB`] = 'grenade',
        [`WEAPON_PROXMINE`] = 'grenade',
        [`WEAPON_SNOWBALL`] = 'grenade',
        [`WEAPON_PIPEBOMB`] = 'grenade',
        [`WEAPON_BALL`] = 'grenade',
        [`WEAPON_SMOKEGRENADE`] = 'grenade',
        [`WEAPON_FLARE`] = 'grenade',
        
        [`WEAPON_KNIFE`] = 'melee',
        [`WEAPON_KNUCKLE`] = 'melee',
        [`WEAPON_NIGHTSTICK`] = 'melee',
        [`WEAPON_HAMMER`] = 'melee',
        [`WEAPON_BAT`] = 'melee',
        [`WEAPON_GOLFCLUB`] = 'melee',
        [`WEAPON_CROWBAR`] = 'melee',
        [`WEAPON_BOTTLE`] = 'melee',
        [`WEAPON_DAGGER`] = 'melee',
        [`WEAPON_HATCHET`] = 'melee',
        [`WEAPON_MACHETE`] = 'melee',
        [`WEAPON_FLASHLIGHT`] = 'melee',
        [`WEAPON_SWITCHBLADE`] = 'melee',
        [`WEAPON_POOLCUE`] = 'melee',
        [`WEAPON_WRENCH`] = 'melee',
        [`WEAPON_BATTLEAXE`] = 'melee',
        [`WEAPON_STONE_HATCHET`] = 'melee'
    }
    
    return weaponTypes[weapon] or 'default'
end

-- nui callbacks
RegisterNuiCallback('uiLoaded', function(data, cb)
    ESX.TriggerServerCallback('final_hudv2:fetchPlayerCount', function(current, max)
        SendNUIMessage({
            type = 'updatePlayerCount',
            current = current,
            max = max
        })
    end)

    UpdateLocation()

    currentVehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    if currentVehicle ~= 0 then
        SendNUIMessage({
            type = 'setVehicleState',
            inVehicle = true,
        })
    end

    repeat Wait(1) until next(ESX.PlayerData) ~= nil and ESX.PlayerData.job ~= nil
    UpdatePlayerData(ESX.PlayerData)
    UpdateJobData(ESX.PlayerData)

    cb({})
end)

RegisterNUICallback('saveHudSettings', function(data, cb)
    TriggerServerEvent('hud:saveSettings', data)
    cb('ok')
end)

RegisterNUICallback('resetHudSettings', function(data, cb)
    TriggerServerEvent('hud:resetSettings')
    cb('ok')
end)

RegisterNUICallback('chatMessage', function(data, cb)
    TriggerServerEvent('chat:addMessage', {
        args = { GetPlayerName(PlayerId()), data.message }
    })
    cb('ok')
end)

RegisterNUICallback('chatActive', function(data, cb)
    chatActive = data.active
    if data.active then
        SetNuiFocus(true, true)
        SetNuiFocusKeepInput(false)
    else
        SetNuiFocus(false, false)
        SetNuiFocusKeepInput(false)
    end
    cb('ok')
end)

RegisterNUICallback('updateChatStatus', function(data, cb)
    chatActive = data.active
    if data.active then
        SetNuiFocus(true, true)
        SetNuiFocusKeepInput(false)
    else
        SetNuiFocus(false, false)
        SetNuiFocusKeepInput(false)
    end
    cb('ok')
end)

RegisterNUICallback('setNuiFocus', function(data, cb)
    SetNuiFocus(data.hasFocus, data.hasCursor)
    SetNuiFocusKeepInput(false)
    cb('ok')
end)

RegisterNUICallback('closeSettings', function(data, cb)
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('saveSettings', function(data, cb)
    TriggerServerEvent('finalcity-hud:saveSettings', data)
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('chatMessage', function(data, cb)
    local playerName = GetPlayerName(PlayerId())
    TriggerServerEvent('chat:addMessage', {
        args = { playerName, data.message }
    })
    cb('ok')
end)

RegisterNUICallback('chatCommand', function(data, cb)
    local command = data.command
    if command and command:sub(1, 1) == '/' then
        local commandString = command:sub(2)
        local args = {}
        local commandName = ''
        
        for word in commandString:gmatch('%S+') do
            if commandName == '' then
                commandName = word
            else
                table.insert(args, word)
            end
        end
        
        if commandName == 'hudsettings' then
            SendNUIMessage({
                type = 'openSettings'
            })
        else
            ExecuteCommand(command:sub(2))
        end
    end
    cb('ok')
end)

-- update loops / threads
CreateThread(function()
    ReplaceHudColour(116, 15)

    while true do
        Wait(5000)
        UpdateLocation()
    end
end)

CreateThread(function() -- less important vehicle update loop
    while true do
        local sleep = 5000

        if currentVehicle ~= 0 then
            sleep = 1000

            local gear = GetVehicleCurrentGear(currentVehicle)
            local fuel = math.floor(GetVehicleFuelLevel(currentVehicle) + 0.5)
            local engine = GetIsVehicleEngineRunning(currentVehicle)
            local _, lightsOn, _ = GetVehicleLightsState(currentVehicle)
            local locked = GetVehicleDoorLockStatus(currentVehicle) == 2

            SendNUIMessage({
                type = 'updateVehicle',
                gear = gear,
                fuel = fuel,
                engine = engine,
                lights = lightsOn == 1,
                locked = locked,
            })
        end

        Wait(sleep)
    end
end)

CreateThread(function() -- more important vehicle update loop (speedometer)
    while true do
        local sleep = 5000

        if currentVehicle ~= 0 then
            sleep = 50

            local speed = math.floor(GetEntitySpeed(currentVehicle) * 3.6)

            SendNUIMessage({
                type = 'updateVehicleSpeed',
                speed = speed,
            })
        end

        Wait(sleep)
    end
end)

CreateThread(function() -- weapon/ammo update loop
    local hadWeapon = false

    while true do
        local sleep = 2000

        local playerPed = PlayerPedId()
        local currentWeapon = GetSelectedPedWeapon(playerPed)
        local weaponType = getWeaponType(currentWeapon)

        if currentWeapon ~= `WEAPON_UNARMED` and weaponType ~= 'melee' then
            sleep = 500

            local ammo = getWeaponAmmo(playerPed, currentWeapon)

            SendNUIMessage({
                type = 'updateWeapon',
                weaponData = {
                    hasWeapon = true,
                    currentAmmo = ammo.clipAmmo,
                    reserveAmmo = ammo.reserveAmmo,
                    maxClipSize = ammo.maxClipSize,
                    type = weaponType,
                }
            })

            hadWeapon = true
        elseif hadWeapon then
            SendNUIMessage({
                type = 'updateWeapon',
                weaponData = {
                    hasWeapon = false,
                }
            })

            hadWeapon = false
        end

        Wait(sleep)
    end
end)

CreateThread(function()
    while true do
        Wait(0)

        if chatActive then
            DisableAllControlActions(0)
            EnableControlAction(0, 1, true)
            EnableControlAction(0, 2, true)
            EnableControlAction(0, 245, true)
            EnableControlAction(0, 322, true)

            if IsControlJustPressed(0, 322) then
                SendNUIMessage({
                    type = 'closeChat'
                })
            end
        else
            Wait(100)
        end
    end
end)

-- state bags
AddStateBagChangeHandler("status", nil, function(bagName, key, value)
    local player = GetPlayerFromStateBagName(bagName)
    if player ~= PlayerId() then
        return
    end

    local hunger = 0
    local thirst = 0

    -- Hito: we add 0.5 to round to the nearest int, because if we just round down then the
    -- hunger/thirst bar will never hit 100% (only 99%) which is not ideal imho
    for _, status in ipairs(value) do
        if status.name == "hunger" then
            hunger = math.floor(status.percent + 0.5)
        elseif status.name == "thirst" then
            thirst = math.floor(status.percent + 0.5)
        end
    end

    SendNUIMessage({
        type = 'updateStatus',
        hunger = hunger,
        thirst = thirst,
    })
end)

-- client events
AddEventHandler('esx:enteredVehicle', function(vehicle, plate, seat, displayName, netId)
    currentVehicle = vehicle
    SendNUIMessage({
        type = 'setVehicleState',
        inVehicle = true,
    })
end)

AddEventHandler('esx:exitedVehicle', function(vehicle, plate, seat, displayName, netId)
    currentVehicle = 0
    SendNUIMessage({
        type = 'setVehicleState',
        inVehicle = false,
    })
end)

function ShowNotification(title, message, type, duration)
    SendNUIMessage({
        type = 'showNotification',
        notification = {
            type = type or 'info',
            title = title or 'Information',
            message = message or '',
            duration = duration or 5000
        }
    })
end

--- @param playerData table
function UpdatePlayerData(playerData)
    local bankMoney = 0

    for _, account in ipairs(playerData.accounts) do
        if account.name == 'bank' then
            bankMoney = account.money
            break
        end
    end

    local currentData = {
        id = GetPlayerServerId(PlayerId()),
        money = playerData.money,
        bankMoney = bankMoney,
        job = playerData.job.label,
        jobGrade = playerData.job.grade_label,
    }

    SendNUIMessage({
        type = 'updatePlayerData',
        playerData = currentData
    })
end

local lastJobData = {}

--- @param playerData table
function UpdateJobData(playerData)
    local currentJob = playerData.job and playerData.job.label or 'Arbeitslos'
    local currentGrade = playerData.job and playerData.job.grade_label or 'Ohne Rang'
    
    if lastJobData.job ~= currentJob or lastJobData.grade ~= currentGrade then
        lastJobData = {
            job = currentJob,
            grade = currentGrade
        }
        
        SendNUIMessage({
            type = 'updateJob',
            job = currentJob,
            grade = currentGrade
        })
    end
end

local lastLocationData = {}

function UpdateLocation()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local streetName, _ = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local street = GetStreetNameFromHashKey(streetName)
    local zone = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z))
    
    if lastLocationData.street ~= street or lastLocationData.zone ~= zone then
        lastLocationData = {
            street = street,
            zone = zone
        }
        
        SendNUIMessage({
            type = 'updateLocation',
            street = street,
            zone = zone
        })
    end
end

RegisterNetEvent('hud:updatePlayerCount', function(current, max)
    SendNUIMessage({
        type = 'updatePlayerCount',
        current = current,
        max = max,
    })
end)

function ShowProgress(text, duration)
    SendNUIMessage({
        type = 'showProgress',
        text = text,
        duration = duration
    })
end

function ShowInteraction(key, text)
    SendNUIMessage({
        type = 'showInteraction',
        text = text
    })
end

function HideInteraction()
    SendNUIMessage({
        type = 'hideInteraction'
    })
end

RegisterKeyMapping('openhudchat', 'Open HUD Chat', 'keyboard', 't')


RegisterCommand('openhudchat', function()
    if not chatActive then
        SendNUIMessage({
            type = 'openChat'
        })
    end
end, false)

RegisterNetEvent('chat:addMessage')
AddEventHandler('chat:addMessage', function(data)
    SendNUIMessage({
        type = 'addChatMessage',
        template = data.template,
        args = data.args
    })
end)

RegisterCommand('hudsettings', function()
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openSettings'
    })
end, false)



RegisterNetEvent('hud:notify')
AddEventHandler('hud:notify', function(type, title, message, duration)
    ShowNotification(title, message, type, duration)
end)

RegisterNetEvent('hud:progress')
AddEventHandler('hud:progress', function(text, duration)
    ShowProgress(text, duration)
end)

RegisterNetEvent('hud:interaction')
AddEventHandler('hud:interaction', function(key, text)
    ShowInteraction(key, text)
end)

RegisterNetEvent('hud:hideInteraction')
AddEventHandler('hud:hideInteraction', function()
    HideInteraction()
end)

RegisterNetEvent('hud:showAnnouncement')
AddEventHandler('hud:showAnnouncement', function(title, message, duration)
    SendNUIMessage({
        type = 'showAnnouncement',
        title = title,
        message = message,
        duration = duration or 15000
    })
    PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", 1)
end)

RegisterNetEvent('hud:announce')
AddEventHandler('hud:announce', function(message)
    SendNUIMessage({
        type = 'showAnnouncement',
        title = 'Ankündigung',
        message = message,
        duration = 15000
    })
    PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", 1)
end)

RegisterNetEvent('finalcity-hud:notify')
AddEventHandler('finalcity-hud:notify', function(title, message, type, timeout)
    ShowNotification(title, message, type, timeout)
    PlaySoundFrontend(-1, "ATM_WINDOW", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
end)

RegisterNetEvent('finalcity-hud:announce')
AddEventHandler('finalcity-hud:announce', function(title, message, timeout)
    SendNUIMessage({
        type = 'showAnnouncement',
        title = title,
        message = message,
        duration = timeout or 5000
    })
    PlaySoundFrontend(-1, "OTHER_TEXT", "HUD_AWARDS", 1)
end)

RegisterNetEvent('finalcity-hud:startProgressbar')
AddEventHandler('finalcity-hud:startProgressbar', function(ms, name)
    SendNUIMessage({
        type = 'startProgressbar',
        duration = ms,
        name = name
    })
end)

RegisterNetEvent('finalcity-hud:stopProgressbar')
AddEventHandler('finalcity-hud:stopProgressbar', function()
    SendNUIMessage({
        type = 'stopProgressbar'
    })
end)

RegisterNetEvent('hud:openSettings')
AddEventHandler('hud:openSettings', function()
    SendNUIMessage({
        type = 'openSettings'
    })
end)

AddEventHandler('esx:onPlayerDeath', function(data)
    SendNUIMessage({
        type = 'toggleHud',
        show = false
    })
end)

AddEventHandler('esx:onPlayerSpawn', function(spawn)
    SendNUIMessage({
        type = 'toggleHud',
        show = true
    })
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function()
    local playerData = ESX.GetPlayerData()

    UpdateJobData(playerData)
    UpdatePlayerData(playerData)
end)

RegisterNetEvent('esx:setAccountMoney')
AddEventHandler('esx:setAccountMoney', function(account)
    local playerData = ESX.GetPlayerData()
    UpdatePlayerData(playerData)
end)

exports('ShowNotification', ShowNotification)
exports('ShowProgress', ShowProgress)
exports('ShowInteraction', ShowInteraction)
exports('HideInteraction', HideInteraction)
exports('ShowAnnouncement', function(title, message, duration)
    TriggerEvent('hud:showAnnouncement', title, message, duration)
end)

exports('Notify', function(title, message, type, timeout)
    ShowNotification(title, message, type, timeout)
end)

exports('Announce', function(title, message, timeout)
    SendNUIMessage({
        type = 'showAnnouncement',
        title = title,
        message = message,
        duration = timeout or 5000
    })
end)

exports('HideHud', function(hide)
    SendNUIMessage({
        type = 'toggleHud',
        show = not hide
    })
end)

exports('HelpNotify', function(message, key)
    SendNUIMessage({
        type = 'showHelpNotify',
        message = message,
        key = key
    })
end)

exports('ShowHelpNotify', function(message, key)
    SendNUIMessage({
        type = 'showHelpNotify',
        message = message,
        key = key
    })
end)

exports('HideHelpNotify', function()
    SendNUIMessage({
        type = 'hideHelpNotify'
    })
end)

exports('StartProgressbar', function(ms, name)
    SendNUIMessage({
        type = 'startProgressbar',
        duration = ms,
        name = name
    })
end)

exports('StopProgressbar', function()
    SendNUIMessage({
        type = 'stopProgressbar'
    })
end)

RegisterNetEvent('SaltyChat_VoiceRangeChanged', function(voiceRange, index)
    TriggerEvent('cc_hud:notify', 'info', 'Saltychat', 'Sprachreichweite: ' .. voiceRange .. 'm', 3000)
    PlaySoundFrontend(-1, "ATM_WINDOW", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
    SendNUIMessage({
        action = "SetVoiceRange",
        range = index + 1
    })
end)

RegisterNetEvent('SaltyChat_TalkStateChanged', function(SaltyisTalking)
    SendNUIMessage({
        action = "voiceStatus",
        status = SaltyisTalking,
        muted = muted
    })
    talking = SaltyisTalking
end)

AddEventHandler('SaltyChat_RadioChannelChanged', function(radioChannel, b)
    SendNUIMessage({
        action = "funk",
        funk = radioChannel ~= nil
    })
end)

RegisterNetEvent('SaltyChat_MicStateChanged', function(micState)
    muted = micState
    SendNUIMessage({
        action = "voiceStatus",
        status = talking,
        muted = muted
    })
end)